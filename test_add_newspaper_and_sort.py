#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试添加报纸并验证排序
"""
import requests
import json

def test_add_newspaper_and_sort():
    """测试添加报纸并验证排序"""
    base_url = "http://127.0.0.1:5009"
    
    print('测试添加报纸并验证排序:')
    
    # 1. 查看当前报纸列表
    print('1. 当前报纸列表:')
    r1 = requests.get(f'{base_url}/api/newspapers')
    if r1.status_code == 200:
        data1 = r1.json()
        if data1['success']:
            newspapers1 = data1['data']
            print(f'   当前有 {len(newspapers1)} 个报纸')
            for i, n in enumerate(newspapers1, 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'   {i}. {name} (ID: {newspaper_id}) - {created_at}')
    
    # 2. 添加一个新报纸
    print('\n2. 添加新报纸:')
    new_newspaper_data = {
        'name': '测试报纸_最新',
        'url': 'http://test.example.com',
        'province': '测试省',
        'city': '测试市',
        'page_link_rule': '//a/@href',
        'page_link_method': 'xpath',
        'image_link_rule': '//img/@src',
        'image_link_method': 'xpath',
        'pdf_link_rule': '//a[contains(@href, ".pdf")]/@href',
        'pdf_link_method': 'xpath',
        'news_link_rule': '//a/@href',
        'news_link_method': 'xpath'
    }
    
    r2 = requests.post(f'{base_url}/api/newspapers', json=new_newspaper_data)
    if r2.status_code == 200:
        result = r2.json()
        if result['success']:
            new_id = result['data']['id']
            print(f'   ✅ 成功添加新报纸，ID: {new_id}')
        else:
            print(f'   ❌ 添加失败: {result.get("message")}')
            return
    else:
        print(f'   ❌ 添加请求失败: {r2.status_code}')
        return
    
    # 3. 再次查看报纸列表，验证排序
    print('\n3. 添加后的报纸列表:')
    r3 = requests.get(f'{base_url}/api/newspapers')
    if r3.status_code == 200:
        data3 = r3.json()
        if data3['success']:
            newspapers3 = data3['data']
            print(f'   现在有 {len(newspapers3)} 个报纸')
            for i, n in enumerate(newspapers3, 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'   {i}. {name} (ID: {newspaper_id}) - {created_at}')
            
            # 检查新添加的报纸是否在第一位
            if newspapers3[0]['id'] == new_id:
                print('\n   ✅ 排序正确：新添加的报纸在第一位')
            else:
                print('\n   ❌ 排序错误：新添加的报纸不在第一位')
                print(f'   第一位是: {newspapers3[0]["name"]} (ID: {newspapers3[0]["id"]})')
                print(f'   新报纸是: {newspapers3[-1]["name"]} (ID: {newspapers3[-1]["id"]})')
    
    # 4. 清理：删除测试报纸
    print('\n4. 清理测试数据:')
    r4 = requests.delete(f'{base_url}/api/newspapers/{new_id}')
    if r4.status_code == 200:
        result = r4.json()
        if result['success']:
            print(f'   ✅ 成功删除测试报纸')
        else:
            print(f'   ❌ 删除失败: {result.get("message")}')
    else:
        print(f'   ❌ 删除请求失败: {r4.status_code}')

if __name__ == "__main__":
    test_add_newspaper_and_sort()

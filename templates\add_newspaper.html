{% extends "base.html" %}

{% block title %}添加报纸 - 报纸采集平台{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('newspapers') }}">报纸管理</a></li>
        <li class="breadcrumb-item active">添加报纸</li>
    </ol>
</nav>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2"></i>添加报纸
    </h1>
    <div>
        <a href="{{ url_for('newspapers') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <form id="newspaper-form">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">报纸名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">省份</label>
                                <input type="text" class="form-control" id="province" name="province">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">城市</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">报纸URL <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="url" name="url" required>
                        <div class="form-text">
                            支持日期占位符：YYYY（年）、MM（月）、DD（日）
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">URL白名单</label>
                                <textarea class="form-control" id="url_whitelist" name="url_whitelist" rows="3" placeholder="每行一个URL模式"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">URL黑名单</label>
                                <textarea class="form-control" id="url_blacklist" name="url_blacklist" rows="3" placeholder="每行一个URL模式"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 采集规则 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>采集规则配置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 版面链接提取规则 -->
                    <div class="mb-4">
                        <h6 class="text-primary">版面链接提取规则</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">提取方法</label>
                                    <select class="form-select" id="page_link_method" name="page_link_method">
                                        <option value="xpath">XPath</option>
                                        <option value="re">正则表达式</option>
                                        <option value="bs4">BeautifulSoup</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label class="form-label">提取规则</label>
                                    <div class="mb-2">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="page_link_rule_filter" placeholder="输入关键词筛选已有规则..."
                                                   autocomplete="off"
                                                   oninput="filterPageLinkRules()" onclick="togglePageLinkRules()" onfocus="showPageLinkRulesOnFocus()">
                                            <div class="dropdown-menu w-100" id="page_link_rule_dropdown" style="max-height: 200px; overflow-y: auto; display: none; position: absolute; top: 100%; left: 0; z-index: 1000;">
                                                <div class="dropdown-item text-muted">正在加载规则...</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <textarea class="form-control" id="page_link_rule" name="page_link_rule" rows="2" placeholder="请输入版面链接提取规则"></textarea>
                                    </div>
                                    <!-- 新增的多链接测试按钮 -->
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-outline-primary" onclick="testPageLinks()">
                                            <i class="fas fa-play me-1"></i>测试
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="testTwoLinks()">
                                            <i class="fas fa-link me-1"></i>测试双链接提取
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="testThreeLinks()">
                                            <i class="fas fa-sitemap me-1"></i>测试三链接提取
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 版面图片链接提取规则 -->
                    <div class="mb-4">
                        <h6 class="text-primary">版面图片链接提取规则</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">提取方法</label>
                                    <select class="form-select" id="image_link_method" name="image_link_method">
                                        <option value="xpath">XPath</option>
                                        <option value="re">正则表达式</option>
                                        <option value="bs4">BeautifulSoup</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label class="form-label">提取规则</label>
                                    <div class="mb-2">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="image_link_rule_filter" placeholder="输入关键词筛选已有规则..."
                                                   autocomplete="off"
                                                   oninput="filterImageLinkRules()" onclick="toggleImageLinkRules()" onfocus="showImageLinkRulesOnFocus()">
                                            <div class="dropdown-menu w-100" id="image_link_rule_dropdown" style="max-height: 200px; overflow-y: auto; display: none; position: absolute; top: 100%; left: 0; z-index: 1000;">
                                                <div class="dropdown-item text-muted">正在加载规则...</div>
                                            </div>
                                        </div>
                                    </div>
                                    <textarea class="form-control" id="image_link_rule" name="image_link_rule" rows="2" placeholder="请输入版面图片链接提取规则"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 版面PDF链接提取规则 -->
                    <div class="mb-4">
                        <h6 class="text-primary">版面PDF链接提取规则</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">提取方法</label>
                                    <select class="form-select" id="pdf_link_method" name="pdf_link_method">
                                        <option value="xpath">XPath</option>
                                        <option value="re">正则表达式</option>
                                        <option value="bs4">BeautifulSoup</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label class="form-label">提取规则</label>
                                    <div class="mb-2">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="pdf_link_rule_filter" placeholder="输入关键词筛选已有规则..."
                                                   autocomplete="off"
                                                   oninput="filterPdfLinkRules()" onclick="togglePdfLinkRules()" onfocus="showPdfLinkRulesOnFocus()">
                                            <div class="dropdown-menu w-100" id="pdf_link_rule_dropdown" style="max-height: 200px; overflow-y: auto; display: none; position: absolute; top: 100%; left: 0; z-index: 1000;">
                                                <div class="dropdown-item text-muted">正在加载规则...</div>
                                            </div>
                                        </div>
                                    </div>
                                    <textarea class="form-control" id="pdf_link_rule" name="pdf_link_rule" rows="2" placeholder="请输入版面PDF链接提取规则"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 版面新闻链接提取规则 -->
                    <div class="mb-4">
                        <h6 class="text-primary">版面新闻链接提取规则</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">提取方法</label>
                                    <select class="form-select" id="news_link_method" name="news_link_method">
                                        <option value="xpath">XPath</option>
                                        <option value="re">正则表达式</option>
                                        <option value="bs4">BeautifulSoup</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label class="form-label">提取规则</label>
                                    <div class="mb-2">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="news_link_rule_filter" placeholder="输入关键词筛选已有规则..."
                                                   autocomplete="off"
                                                   oninput="filterNewsLinkRules()" onclick="toggleNewsLinkRules()" onfocus="showNewsLinkRulesOnFocus()">
                                            <div class="dropdown-menu w-100" id="news_link_rule_dropdown" style="max-height: 200px; overflow-y: auto; display: none; position: absolute; top: 100%; left: 0; z-index: 1000;">
                                                <div class="dropdown-item text-muted">正在加载规则...</div>
                                            </div>
                                        </div>
                                    </div>
                                    <textarea class="form-control" id="news_link_rule" name="news_link_rule" rows="2" placeholder="请输入版面新闻链接提取规则"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试按钮 -->
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-info me-2" onclick="testPageContent()">
                            <i class="fas fa-vial me-1"></i>测试版面内容提取
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="testFullCollection()">
                            <i class="fas fa-play-circle me-1"></i>测试完整采集流程
                        </button>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-save me-1"></i>保存报纸
                    </button>
                    <a href="{{ url_for('newspapers') }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-times me-1"></i>取消
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- 测试结果面板 -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-flask me-2"></i>测试结果
                </h5>
            </div>
            <div class="card-body">
                <div id="test-results">
                    <p class="text-muted text-center">点击测试按钮查看结果</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/add_newspaper.js') }}"></script>
{% endblock %}

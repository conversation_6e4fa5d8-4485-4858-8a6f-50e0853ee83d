# -*- coding: utf-8 -*-
"""
数据库模型定义
"""
from datetime import datetime
from database import db

class Newspaper(db.Model):
    """报纸模型"""
    __tablename__ = 'newspapers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True, comment='报纸名称')
    url = db.Column(db.Text, nullable=False, comment='报纸URL')
    province = db.Column(db.String(50), nullable=True, index=True, comment='省份')
    city = db.Column(db.String(50), nullable=True, index=True, comment='城市')
    url_whitelist = db.Column(db.Text, nullable=True, comment='URL白名单')
    url_blacklist = db.Column(db.Text, nullable=True, comment='URL黑名单')
    
    # 采集规则字段
    page_link_rule = db.Column(db.Text, nullable=True, comment='版面链接提取规则')
    page_link_method = db.Column(db.String(20), default='xpath', comment='版面链接提取方法(re/xpath/bs4)')
    
    image_link_rule = db.Column(db.Text, nullable=True, comment='版面图片链接提取规则')
    image_link_method = db.Column(db.String(20), default='xpath', comment='图片链接提取方法')
    
    pdf_link_rule = db.Column(db.Text, nullable=True, comment='版面PDF链接提取规则')
    pdf_link_method = db.Column(db.String(20), default='xpath', comment='PDF链接提取方法')
    
    news_link_rule = db.Column(db.Text, nullable=True, comment='版面新闻链接提取规则')
    news_link_method = db.Column(db.String(20), default='xpath', comment='新闻链接提取方法')
    
    # 状态字段
    collection_status = db.Column(db.String(20), default='未采集', index=True, comment='采集状态')
    last_collection_time = db.Column(db.DateTime, nullable=True, index=True, comment='最后采集时间')
    collection_duration = db.Column(db.Integer, default=0, comment='采集运行时间(秒)')
    publish_status = db.Column(db.String(20), default='未发布', index=True, comment='发布状态')
    is_active = db.Column(db.Boolean, default=True, index=True, comment='是否启用')

    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'url': self.url,
            'province': self.province,
            'city': self.city,
            'url_whitelist': self.url_whitelist,
            'url_blacklist': self.url_blacklist,
            'page_link_rule': self.page_link_rule,
            'page_link_method': self.page_link_method,
            'image_link_rule': self.image_link_rule,
            'image_link_method': self.image_link_method,
            'pdf_link_rule': self.pdf_link_rule,
            'pdf_link_method': self.pdf_link_method,
            'news_link_rule': self.news_link_rule,
            'news_link_method': self.news_link_method,
            'collection_status': self.collection_status,
            'last_collection_time': self.last_collection_time.isoformat() if self.last_collection_time else None,
            'collection_duration': self.collection_duration,
            'publish_status': self.publish_status,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class CollectionRule(db.Model):
    """采集规则模型"""
    __tablename__ = 'collection_rules'
    
    id = db.Column(db.Integer, primary_key=True)
    newspaper_id = db.Column(db.Integer, db.ForeignKey('newspapers.id'), nullable=False)
    rule_type = db.Column(db.String(50), nullable=False, comment='规则类型(page_link/image_link/pdf_link/news_link)')
    rule_content = db.Column(db.Text, nullable=False, comment='规则内容')
    extraction_method = db.Column(db.String(20), nullable=False, comment='提取方法(re/xpath/bs4)')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    newspaper = db.relationship('Newspaper', backref=db.backref('rules', lazy=True))

class CollectionResult(db.Model):
    """采集结果模型"""
    __tablename__ = 'collection_results'

    id = db.Column(db.Integer, primary_key=True)
    newspaper_id = db.Column(db.Integer, db.ForeignKey('newspapers.id'), nullable=False, index=True)
    newspaper_name = db.Column(db.String(200), nullable=False, index=True, comment='报纸名称')
    newspaper_url = db.Column(db.Text, nullable=False, comment='报纸链接')
    province = db.Column(db.String(50), nullable=True, index=True, comment='省份')
    city = db.Column(db.String(50), nullable=True, index=True, comment='城市')

    page_link = db.Column(db.Text, nullable=True, comment='版面链接')
    page_title = db.Column(db.String(500), nullable=True, comment='版面标题')
    image_link = db.Column(db.Text, nullable=True, comment='版面图片链接')
    pdf_link = db.Column(db.Text, nullable=True, comment='版面PDF链接')
    news_links = db.Column(db.Text, nullable=True, comment='版面新闻链接(JSON格式)')
    news_count = db.Column(db.Integer, default=0, comment='新闻数量')

    collection_time = db.Column(db.DateTime, default=datetime.utcnow, index=True, comment='采集时间')
    collection_date = db.Column(db.Date, default=datetime.utcnow().date, index=True, comment='采集日期')
    publish_status = db.Column(db.String(20), default='未发布', index=True, comment='发布状态')
    collection_status = db.Column(db.String(20), default='成功', comment='采集状态')
    error_message = db.Column(db.Text, nullable=True, comment='错误信息')
    
    # 关联关系
    newspaper = db.relationship('Newspaper', backref=db.backref('results', lazy=True))
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'newspaper_id': self.newspaper_id,
            'newspaper_name': self.newspaper_name,
            'newspaper_url': self.newspaper_url,
            'province': self.province,
            'city': self.city,
            'page_link': self.page_link,
            'page_title': self.page_title,
            'image_link': self.image_link,
            'pdf_link': self.pdf_link,
            'news_links': self.news_links,
            'news_count': self.news_count,
            'collection_time': self.collection_time.isoformat() if self.collection_time else None,
            'collection_date': self.collection_date.isoformat() if self.collection_date else None,
            'publish_status': self.publish_status,
            'collection_status': self.collection_status,
            'error_message': self.error_message
        }

class CollectionTask(db.Model):
    """采集任务模型"""
    __tablename__ = 'collection_tasks'

    id = db.Column(db.Integer, primary_key=True)
    task_name = db.Column(db.String(200), nullable=False, comment='任务名称')
    task_type = db.Column(db.String(20), nullable=False, comment='任务类型(single/batch/scheduled)')
    newspaper_ids = db.Column(db.Text, nullable=False, comment='报纸ID列表(JSON格式)')

    status = db.Column(db.String(20), default='待执行', index=True, comment='任务状态')
    start_time = db.Column(db.DateTime, nullable=True, comment='开始时间')
    end_time = db.Column(db.DateTime, nullable=True, comment='结束时间')
    duration = db.Column(db.Integer, default=0, comment='执行时长(秒)')

    total_count = db.Column(db.Integer, default=0, comment='总数量')
    success_count = db.Column(db.Integer, default=0, comment='成功数量')
    failed_count = db.Column(db.Integer, default=0, comment='失败数量')

    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_name': self.task_name,
            'task_type': self.task_type,
            'newspaper_ids': self.newspaper_ids,
            'status': self.status,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': self.duration,
            'total_count': self.total_count,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

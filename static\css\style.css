/* 自定义样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 统计卡片样式 */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 状态标签样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-未采集 {
    background-color: #6c757d;
}

.status-采集中 {
    background-color: #ffc107;
    color: #000;
}

.status-采集成功 {
    background-color: #198754;
}

.status-采集失败 {
    background-color: #dc3545;
}

.status-已发布 {
    background-color: #0d6efd;
}

.status-未发布 {
    background-color: #6c757d;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 表单样式 */
.form-control,
.form-select {
    border-radius: 0.375rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 消息提示样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 测试结果样式 */
.test-result {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.test-result.success {
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
}

.test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
}

.test-result pre {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-sm > .btn,
    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.6875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 粘性定位 */
.sticky-top {
    position: sticky;
    top: 20px;
    z-index: 1020;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

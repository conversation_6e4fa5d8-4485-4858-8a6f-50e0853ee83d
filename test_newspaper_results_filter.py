#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试报纸管理查看结果功能
"""
import requests
import json

def test_newspaper_results_filter():
    """测试报纸管理查看结果功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔍 测试报纸管理查看结果功能")
    print("=" * 60)
    
    # 1. 测试报纸管理页面
    print("1️⃣ 测试报纸管理页面...")
    try:
        response = requests.get(f"{base_url}/newspapers", timeout=10)
        if response.status_code == 200:
            print("   ✅ 报纸管理页面可访问")
            
            # 检查查看结果按钮
            content = response.text
            if 'viewResults' in content and 'fas fa-eye' in content:
                print("   ✅ 查看结果按钮存在")
            else:
                print("   ❌ 查看结果按钮缺失")
                
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试采集结果页面
    print("\n2️⃣ 测试采集结果页面...")
    try:
        response = requests.get(f"{base_url}/collection_results", timeout=10)
        if response.status_code == 200:
            print("   ✅ 采集结果页面可访问")
            
            # 检查筛选功能相关元素
            content = response.text
            filter_features = [
                ('initFromUrlParams', 'URL参数初始化函数'),
                ('updatePageTitle', '页面标题更新函数'),
                ('addBackButton', '返回按钮添加函数'),
                ('showCurrentFilter', '当前筛选显示函数'),
                ('clearNewspaperFilter', '清除筛选函数'),
                ('goBackToNewspapers', '返回报纸管理函数'),
                ('filter-newspaper-name', '报纸名称筛选框')
            ]
            
            print("   🔍 检查筛选功能元素:")
            for feature, description in filter_features:
                if feature in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 3. 测试API功能
    print("\n3️⃣ 测试API功能...")
    
    # 3.1 测试获取报纸列表
    print("   📋 测试获取报纸列表...")
    try:
        response = requests.get(f"{base_url}/api/newspapers", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                newspapers = result.get('data', [])
                print(f"   ✅ 获取到 {len(newspapers)} 个报纸")
                
                if newspapers:
                    test_newspaper = newspapers[0]
                    newspaper_id = test_newspaper['id']
                    newspaper_name = test_newspaper['name']
                    print(f"   📰 测试报纸: {newspaper_name} (ID: {newspaper_id})")
                    
                    # 3.2 测试获取单个报纸信息
                    print("   📰 测试获取单个报纸信息...")
                    response = requests.get(f"{base_url}/api/newspapers/{newspaper_id}", timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            print("   ✅ 获取单个报纸信息成功")
                        else:
                            print(f"   ❌ 获取单个报纸信息失败: {result.get('message')}")
                    else:
                        print(f"   ❌ 获取单个报纸信息API失败: {response.status_code}")
                    
                    # 3.3 测试采集结果筛选
                    print("   🔍 测试采集结果筛选...")
                    
                    # 测试不带筛选的采集结果
                    response = requests.get(f"{base_url}/api/collection/results", timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            all_results = result.get('data', [])
                            print(f"   ✅ 获取到 {len(all_results)} 个采集结果")
                        else:
                            print(f"   ❌ 获取采集结果失败: {result.get('message')}")
                    else:
                        print(f"   ❌ 采集结果API失败: {response.status_code}")
                    
                    # 测试带newspaper_id筛选的采集结果
                    response = requests.get(f"{base_url}/api/collection/results?newspaper_id={newspaper_id}", timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            filtered_results = result.get('data', [])
                            print(f"   ✅ 筛选后获取到 {len(filtered_results)} 个采集结果")
                            
                            # 验证筛选结果是否正确
                            if filtered_results:
                                for result_item in filtered_results:
                                    if result_item.get('newspaper_id') != newspaper_id:
                                        print(f"   ❌ 筛选结果错误: 期望newspaper_id={newspaper_id}, 实际={result_item.get('newspaper_id')}")
                                        break
                                else:
                                    print("   ✅ 筛选结果正确，所有结果都属于指定报纸")
                            else:
                                print("   ℹ️ 该报纸暂无采集结果")
                        else:
                            print(f"   ❌ 筛选采集结果失败: {result.get('message')}")
                    else:
                        print(f"   ❌ 筛选采集结果API失败: {response.status_code}")
                    
                    # 3.4 测试带newspaper_name筛选的采集结果
                    print("   📝 测试报纸名称筛选...")
                    response = requests.get(f"{base_url}/api/collection/results?newspaper_name={newspaper_name}", timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            name_filtered_results = result.get('data', [])
                            print(f"   ✅ 按名称筛选获取到 {len(name_filtered_results)} 个采集结果")
                        else:
                            print(f"   ❌ 按名称筛选失败: {result.get('message')}")
                    else:
                        print(f"   ❌ 按名称筛选API失败: {response.status_code}")
                        
                else:
                    print("   ℹ️ 暂无报纸数据，无法测试筛选功能")
            else:
                print(f"   ❌ 获取报纸列表失败: {result.get('message')}")
        else:
            print(f"   ❌ 报纸列表API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
    
    # 4. 测试URL跳转功能
    print("\n4️⃣ 测试URL跳转功能...")
    try:
        # 模拟点击查看结果按钮的跳转
        test_newspaper_id = 1  # 假设存在ID为1的报纸
        target_url = f"{base_url}/collection_results?newspaper_id={test_newspaper_id}"
        
        response = requests.get(target_url, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ 带参数的采集结果页面可访问: {target_url}")
            
            # 检查页面是否包含JavaScript处理
            content = response.text
            if 'newspaper_id' in content and 'URLSearchParams' in content:
                print("   ✅ 页面包含URL参数处理逻辑")
            else:
                print("   ❌ 页面缺少URL参数处理逻辑")
        else:
            print(f"   ❌ 带参数的采集结果页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ URL跳转测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 报纸管理查看结果功能测试完成！")
    
    print("\n📋 功能特性总结:")
    print("✅ 查看结果按钮 - 每份报纸都有查看结果按钮")
    print("✅ 自动跳转 - 点击后跳转到采集结果页面")
    print("✅ 自动筛选 - 自动筛选当前报纸的采集结果")
    print("✅ URL参数传递 - 通过newspaper_id参数传递报纸信息")
    print("✅ 页面标题更新 - 显示当前查看的报纸名称")
    print("✅ 返回按钮 - 提供返回报纸管理的按钮")
    print("✅ 筛选状态显示 - 显示当前筛选条件")
    print("✅ 清除筛选 - 可以清除筛选查看所有结果")
    
    print("\n🎯 用户体验:")
    print("1. 在报纸管理页面点击任意报纸的'查看结果'按钮")
    print("2. 自动跳转到采集结果页面")
    print("3. 页面自动筛选显示该报纸的采集结果")
    print("4. 页面标题显示当前查看的报纸名称")
    print("5. 提供返回按钮和清除筛选功能")
    
    print("\n🔧 技术实现:")
    print("- 前端: JavaScript获取URL参数，自动设置筛选条件")
    print("- 后端: API支持newspaper_id参数筛选")
    print("- 界面: 动态更新页面标题和添加操作按钮")
    print("- 导航: 提供返回和清除筛选功能")
    
    print("\n💡 使用场景:")
    print("- 查看特定报纸的所有采集结果")
    print("- 快速从报纸管理跳转到相关采集结果")
    print("- 分析单个报纸的采集效果")
    print("- 管理特定报纸的发布状态")
    
    return True

if __name__ == "__main__":
    test_newspaper_results_filter()

================================================================
                 Newspaper Collection Platform
                      Quick Start Guide
================================================================

IMPORTANT: If you see Chinese character encoding issues in BAT files,
use the new simplified scripts listed below.

================================================================
                      Quick Setup Steps
================================================================

1. FIRST TIME SETUP:
   - Double-click: menu.bat
   - Select option 1: Setup Environment
   - Wait for completion
   
2. CONFIGURE DATABASE:
   - Edit config.py file
   - Change MYSQL_PASSWORD to your MySQL password
   - Save the file
   
3. INITIALIZE DATABASE:
   - In menu.bat, select option 2: Initialize Database
   - Wait for completion
   
4. START SERVER:
   - In menu.bat, select option 3: Start Server
   - Keep the window open
   
5. ACCESS SYSTEM:
   - Open browser to: http://localhost:5000
   - Or use menu.bat option 5: Open Browser

================================================================
                      Available Scripts
================================================================

MAIN MENU:
  menu.bat                 - Main menu interface

SETUP SCRIPTS:
  setup_environment.bat    - Install Python environment
  init_db_simple.bat      - Initialize database
  
RUNNING SCRIPTS:
  start_simple.bat        - Start server quickly
  check_system.bat        - Check system status

LEGACY SCRIPTS (may have encoding issues):
  开始使用.bat            - Original Chinese menu
  setup_env.bat           - Original setup script
  start_server.bat        - Original start script

================================================================
                      System Requirements
================================================================

1. Python 3.7 or higher
   Download: https://www.python.org/downloads/
   
2. MySQL Database
   Option A: MySQL Community Server
   Option B: XAMPP (includes MySQL)
   Option C: Docker MySQL

3. Modern web browser

================================================================
                      Configuration
================================================================

Edit config.py file:

MYSQL_HOST = 'localhost'        # MySQL server address
MYSQL_PORT = 3306               # MySQL port  
MYSQL_USER = 'root'             # Database username
MYSQL_PASSWORD = 'your_password' # Change this to your password
MYSQL_DATABASE = 'newspaper_db'  # Database name

================================================================
                      Troubleshooting
================================================================

PROBLEM: Python not found
SOLUTION: Install Python and add to PATH

PROBLEM: MySQL connection failed  
SOLUTION: 
  - Check MySQL service is running
  - Verify config.py settings
  - Check username/password

PROBLEM: Dependencies installation failed
SOLUTION:
  - Check internet connection
  - Try running setup_environment.bat again

PROBLEM: Server won't start
SOLUTION:
  - Run check_system.bat to diagnose
  - Check if port 5000 is available

PROBLEM: Chinese characters show as garbage
SOLUTION:
  - Use menu.bat instead of 开始使用.bat
  - Use simplified scripts listed above

================================================================
                      Getting Help
================================================================

1. Run check_system.bat for diagnostics
2. Check README.md for detailed documentation
3. View error messages in console
4. Ensure all requirements are met

================================================================
                      Project Structure
================================================================

Key Files:
  menu.bat              - Main menu
  config.py             - Database configuration
  run.py                - Application entry point
  requirements.txt      - Python dependencies
  
Directories:
  venv/                 - Python virtual environment
  templates/            - HTML templates
  static/               - CSS, JavaScript files
  collector/            - Collection scripts

================================================================

For detailed documentation, see README.md
For Chinese documentation, see 使用指南.md

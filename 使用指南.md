# 报纸采集平台 - 使用指南

## 🚀 快速开始

### 方法一：使用图形化菜单（推荐）
1. 双击运行 `开始使用.bat`
2. 按照菜单提示逐步操作

### 方法二：手动执行
1. 运行 `setup_env.bat` - 安装Python环境
2. 运行 `init_database.bat` - 初始化数据库
3. 运行 `start_server.bat` - 启动服务器
4. 打开浏览器访问 http://localhost:5000

## 📋 详细安装步骤

### 1. 环境准备

#### 安装Python
- 下载Python 3.7+：https://www.python.org/downloads/
- 安装时勾选"Add Python to PATH"
- 验证安装：打开命令行输入 `python --version`

#### 安装MySQL
选择以下任一方式：

**方式1：MySQL官方版本**
- 下载：https://dev.mysql.com/downloads/mysql/
- 安装时记住root密码
- 确保MySQL服务自动启动

**方式2：XAMPP（推荐新手）**
- 下载：https://www.apachefriends.org/download.html
- 安装后启动Apache和MySQL服务
- 访问 http://localhost/phpmyadmin 管理数据库

**方式3：Docker**
```bash
docker run --name mysql-newspaper -e MYSQL_ROOT_PASSWORD=password -e MYSQL_DATABASE=newspaper_db -p 3306:3306 -d mysql:8.0
```

### 2. 项目配置

#### 配置数据库连接
编辑 `config.py` 文件：
```python
MYSQL_HOST = 'localhost'        # MySQL服务器地址
MYSQL_PORT = 3306               # MySQL端口
MYSQL_USER = 'root'             # 数据库用户名
MYSQL_PASSWORD = 'your_password' # 数据库密码（改为你的密码）
MYSQL_DATABASE = 'newspaper_db'  # 数据库名称
```

### 3. 安装和启动

#### 自动安装（推荐）
```batch
# 1. 安装Python环境和依赖包
setup_env.bat

# 2. 初始化数据库
init_database.bat

# 3. 启动服务器
start_server.bat
```

#### 手动安装
```batch
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate.bat

# 安装依赖包
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动服务器
python run.py
```

## 🎯 系统使用

### 首页功能
- 📊 查看采集统计数据
- 🚀 快速启动批量采集
- 📍 省份分布统计

### 报纸管理
1. **添加报纸**
   - 点击"添加报纸"按钮
   - 填写基本信息（名称、URL、省市）
   - 配置采集规则
   - 使用测试功能验证规则

2. **编辑报纸**
   - 在报纸列表中点击"编辑"按钮
   - 修改信息和规则
   - 重新测试验证

3. **删除报纸**
   - 单个删除：点击"删除"按钮
   - 批量删除：选中多个后点击"批量删除"

### 采集规则配置

#### 日期占位符
在报纸URL中使用以下占位符：
- `YYYY` - 四位年份（如：2024）
- `MM` - 两位月份（如：01）
- `DD` - 两位日期（如：15）

示例：`http://paper.people.com.cn/rmrb/html/YYYY-MM/DD/nbs.D110000renmrb_01.htm`

#### 提取方法

**1. XPath（推荐）**
```xpath
# 版面链接
//div[@class="swiper-slide"]//a/@href

# 图片链接
//div[@class="pic"]//img/@src

# PDF链接
//a[contains(@href, ".pdf")]/@href

# 新闻链接
//div[@class="news"]//a/@href
```

**2. 正则表达式**
```regex
# 版面链接
href="([^"]*\.html)"

# 图片链接
src="([^"]*\.jpg)"

# PDF链接
href="([^"]*\.pdf)"
```

**3. BeautifulSoup**
```css
# CSS选择器
css:.swiper-slide a

# 属性选择器
attr:img:src
attr:a:href

# 标签选择器
tag:a
```

### 测试功能

#### 版面链接测试
- 验证能否从报纸首页正确提取版面链接
- 显示提取到的链接数量和前10个链接

#### 版面内容测试
- 验证能否从版面页提取图片、PDF、新闻链接
- 需要先配置版面链接提取规则

#### 完整采集测试
- 测试完整的采集流程
- 显示每个版面的采集结果

### 采集操作

#### 单份采集
1. 在报纸列表中找到目标报纸
2. 点击"单份采集"按钮
3. 等待采集完成

#### 批量采集
1. 在报纸列表中选择多份报纸
2. 点击"批量采集"按钮
3. 确认后开始采集

#### 定时采集
- 在首页点击"开始批量采集"
- 系统会采集所有启用的报纸

### 结果管理

#### 查看结果
- 访问"采集结果"页面
- 使用筛选条件查找特定结果
- 点击"查看详情"查看完整信息

#### 发布管理
- 单个发布：点击发布/取消发布按钮
- 批量发布：选中多个结果后批量操作

## 🔧 故障排除

### 常见问题

**1. 服务器启动失败**
- 检查Python是否正确安装
- 确认虚拟环境已创建：运行 `setup_env.bat`
- 检查端口5000是否被占用

**2. 数据库连接失败**
- 确认MySQL服务已启动
- 检查 `config.py` 中的数据库配置
- 验证数据库用户权限

**3. 采集失败**
- 检查网络连接
- 验证采集规则是否正确
- 使用测试功能调试规则
- 检查目标网站是否可访问

**4. 页面无法访问**
- 确认服务器已启动
- 检查防火墙设置
- 尝试使用 http://127.0.0.1:5000

### 环境检查
运行 `check_env.bat` 检查系统环境：
- Python安装状态
- 依赖包安装状态
- 数据库连接状态
- 项目文件完整性

### 系统测试
运行 `test_system.bat` 进行功能测试：
- API接口测试
- 数据库操作测试
- 采集功能测试

## 📁 文件说明

### 启动脚本
- `开始使用.bat` - 主菜单，图形化操作界面
- `setup_env.bat` - 环境安装脚本
- `start_server.bat` - 快速启动服务器
- `run_with_venv.bat` - 详细启动脚本

### 管理脚本
- `install_mysql.bat` - MySQL安装助手
- `init_database.bat` - 数据库初始化
- `check_env.bat` - 环境检查
- `test_system.bat` - 系统测试

### 核心文件
- `app.py` - Flask主应用
- `run.py` - 应用启动文件
- `config.py` - 配置文件
- `models.py` - 数据库模型
- `api.py` - API接口

### 目录结构
- `templates/` - HTML模板
- `static/` - 静态文件（CSS、JS）
- `collector/` - 采集器模块
- `venv/` - Python虚拟环境

## 🆘 技术支持

如遇到问题：
1. 查看控制台错误信息
2. 运行环境检查脚本
3. 查看详细文档 `README.md`
4. 联系技术支持

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 完整的报纸采集功能
- 图形化管理界面
- 自动化安装脚本

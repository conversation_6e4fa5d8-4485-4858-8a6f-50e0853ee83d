# -*- coding: utf-8 -*-
"""
增强版报纸采集器
"""
import requests
import re
import json
import time
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from lxml import html, etree
from bs4 import BeautifulSoup
from .utils import URLUtils, RateLimiter, ContentCleaner, ValidationUtils, LogUtils

class EnhancedNewspaperCollector:
    """增强版报纸采集器"""
    
    def __init__(self, config=None):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 配置参数
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 5)
        
        # 频率限制器
        self.rate_limiter = RateLimiter(
            min_delay=self.config.get('min_delay', 1),
            max_delay=self.config.get('max_delay', 3)
        )
        
        # 统计信息
        self.stats = {
            'requests_made': 0,
            'requests_failed': 0,
            'pages_extracted': 0,
            'extraction_errors': 0
        }
    
    def _get_page_content(self, url, retries=None):
        """获取页面内容（带重试机制）"""
        if retries is None:
            retries = self.max_retries
        
        for attempt in range(retries + 1):
            try:
                # 频率限制
                self.rate_limiter.wait()
                
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                # 自动检测编码
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding
                
                self.stats['requests_made'] += 1
                return response.text
                
            except requests.exceptions.RequestException as e:
                self.stats['requests_failed'] += 1
                
                if attempt < retries:
                    LogUtils.log_error(f"获取页面失败，{self.retry_delay}秒后重试 (尝试 {attempt + 1}/{retries + 1}): {url}", e)
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(f"获取页面内容失败 (已重试{retries}次): {str(e)}")
    
    def _extract_links_by_regex(self, content, pattern):
        """使用正则表达式提取链接"""
        try:
            # 支持多种正则表达式模式
            flags = re.IGNORECASE | re.DOTALL | re.MULTILINE
            matches = re.findall(pattern, content, flags)
            
            # 处理不同的匹配结果格式
            if not matches:
                return []
            
            # 如果匹配结果是元组，取第一个元素
            if isinstance(matches[0], tuple):
                matches = [match[0] for match in matches]
            
            return [match for match in matches if match]
            
        except re.error as e:
            raise Exception(f"正则表达式错误: {str(e)}")
        except Exception as e:
            raise Exception(f"正则表达式提取失败: {str(e)}")
    
    def _extract_links_by_xpath(self, content, xpath):
        """使用XPath提取链接"""
        try:
            tree = html.fromstring(content)
            results = tree.xpath(xpath)
            
            # 处理不同类型的XPath结果
            links = []
            for result in results:
                if hasattr(result, 'text_content'):
                    # 元素节点
                    text = result.text_content().strip()
                    if text:
                        links.append(text)
                elif hasattr(result, 'strip'):
                    # 字符串结果
                    text = result.strip()
                    if text:
                        links.append(text)
                else:
                    # 其他类型，转换为字符串
                    text = str(result).strip()
                    if text:
                        links.append(text)
            
            return links
            
        except etree.XPathEvalError as e:
            raise Exception(f"XPath语法错误: {str(e)}")
        except Exception as e:
            raise Exception(f"XPath提取失败: {str(e)}")
    
    def _extract_links_by_bs4(self, content, selector):
        """使用BeautifulSoup提取链接"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 支持多种选择器格式
            if selector.startswith('css:'):
                # CSS选择器
                css_selector = selector[4:].strip()
                elements = soup.select(css_selector)
            elif selector.startswith('tag:'):
                # 标签选择器
                tag_name = selector[4:].strip()
                elements = soup.find_all(tag_name)
            elif selector.startswith('attr:'):
                # 属性选择器 格式: attr:tag_name:attr_name
                parts = selector.split(':')
                if len(parts) >= 3:
                    tag_name = parts[1]
                    attr_name = parts[2]
                    elements = soup.find_all(tag_name)
                    return [elem.get(attr_name) for elem in elements if elem.get(attr_name)]
                else:
                    raise Exception("属性选择器格式错误，应为 attr:tag_name:attr_name")
            else:
                # 默认作为CSS选择器处理
                elements = soup.select(selector)
            
            # 提取链接
            links = []
            for element in elements:
                # 优先获取href属性
                if element.get('href'):
                    links.append(element.get('href'))
                # 其次获取src属性
                elif element.get('src'):
                    links.append(element.get('src'))
                # 最后获取文本内容
                else:
                    text = element.get_text().strip()
                    if text:
                        links.append(text)
            
            return links
            
        except Exception as e:
            raise Exception(f"BeautifulSoup提取失败: {str(e)}")
    
    def _extract_links(self, content, rule, method, base_url=None):
        """根据方法提取链接"""
        if not rule or not rule.strip():
            return []
        
        try:
            if method == 're':
                links = self._extract_links_by_regex(content, rule)
            elif method == 'xpath':
                links = self._extract_links_by_xpath(content, rule)
            elif method == 'bs4':
                links = self._extract_links_by_bs4(content, rule)
            else:
                raise Exception(f"不支持的提取方法: {method}")
            
            # 标准化URL
            if base_url:
                normalized_links = []
                for link in links:
                    normalized_url = URLUtils.normalize_url(link, base_url)
                    if normalized_url:
                        normalized_links.append(normalized_url)
                return normalized_links
            
            return links
            
        except Exception as e:
            self.stats['extraction_errors'] += 1
            raise e
    
    def collect_newspaper_by_date(self, newspaper, target_date=None):
        """按指定日期采集报纸"""
        try:
            start_time = time.time()
            LogUtils.log_collection_start(newspaper.name)
            
            # 替换日期占位符
            url = URLUtils.replace_date_placeholders(newspaper.url, target_date)
            
            # 获取首页内容
            content = self._get_page_content(url)
            
            # 提取版面链接
            page_links = self._extract_links(
                content, 
                newspaper.page_link_rule, 
                newspaper.page_link_method,
                url
            )
            
            # 应用URL过滤
            page_links = URLUtils.filter_urls(
                page_links, 
                newspaper.url_whitelist, 
                newspaper.url_blacklist
            )
            
            if not page_links:
                raise Exception('未能获取到有效的版面链接')
            
            # 采集每个版面的内容
            results = []
            for i, page_link in enumerate(page_links):
                try:
                    page_result = self._collect_page_content(newspaper, page_link)
                    if page_result:
                        results.append(page_result)
                        self.stats['pages_extracted'] += 1
                        
                        # 记录提取统计
                        counts = {
                            'images': 1 if page_result.get('image_link') else 0,
                            'pdfs': 1 if page_result.get('pdf_link') else 0,
                            'news': len(page_result.get('news_links', []))
                        }
                        LogUtils.log_page_extraction(page_link, True, counts)
                    
                except Exception as e:
                    LogUtils.log_page_extraction(page_link, False)
                    # 单个版面失败不影响整体采集
                    continue
            
            duration = int(time.time() - start_time)
            
            if results:
                LogUtils.log_collection_end(newspaper.name, True, duration, f"成功采集{len(results)}个版面")
                return {
                    'success': True,
                    'data': results,
                    'duration': duration,
                    'stats': self.stats.copy(),
                    'message': f'采集完成，共采集{len(results)}个版面'
                }
            else:
                raise Exception('所有版面采集都失败了')
                
        except Exception as e:
            duration = int(time.time() - start_time) if 'start_time' in locals() else 0
            LogUtils.log_collection_end(newspaper.name, False, duration, str(e))
            return {
                'success': False,
                'message': str(e),
                'duration': duration,
                'stats': self.stats.copy()
            }
    
    def _collect_page_content(self, newspaper, page_url):
        """采集单个版面的内容"""
        try:
            page_content = self._get_page_content(page_url)
            
            # 提取版面标题
            page_title = self._extract_page_title(page_content) or \
                        ContentCleaner.extract_title_from_url(page_url)
            
            # 提取各种链接
            image_links = self._extract_links(
                page_content,
                newspaper.image_link_rule,
                newspaper.image_link_method,
                page_url
            )
            
            pdf_links = self._extract_links(
                page_content,
                newspaper.pdf_link_rule,
                newspaper.pdf_link_method,
                page_url
            )
            
            news_links = self._extract_links(
                page_content,
                newspaper.news_link_rule,
                newspaper.news_link_method,
                page_url
            )
            
            # 应用URL过滤
            image_links = URLUtils.filter_urls(image_links, newspaper.url_whitelist, newspaper.url_blacklist)
            pdf_links = URLUtils.filter_urls(pdf_links, newspaper.url_whitelist, newspaper.url_blacklist)
            news_links = URLUtils.filter_urls(news_links, newspaper.url_whitelist, newspaper.url_blacklist)
            
            return {
                'page_link': page_url,
                'page_title': ContentCleaner.clean_text(page_title) if page_title else None,
                'image_link': image_links[0] if image_links else None,
                'pdf_link': pdf_links[0] if pdf_links else None,
                'news_links': news_links[:50]  # 限制新闻链接数量
            }
            
        except Exception as e:
            raise Exception(f"采集版面内容失败: {str(e)}")
    
    def _extract_page_title(self, content):
        """提取版面标题"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 尝试多种方式提取标题
            title_selectors = [
                'title',
                'h1',
                'h2',
                '.title',
                '.page-title',
                '[class*="title"]'
            ]
            
            for selector in title_selectors:
                element = soup.select_one(selector)
                if element:
                    title = element.get_text().strip()
                    if title and len(title) > 2:
                        return title
            
            return None
        except:
            return None
    
    def get_stats(self):
        """获取采集统计信息"""
        return self.stats.copy()

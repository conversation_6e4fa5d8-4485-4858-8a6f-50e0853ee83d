<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="205">
            <item index="0" class="java.lang.String" itemvalue="protobuf" />
            <item index="1" class="java.lang.String" itemvalue="shapely" />
            <item index="2" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="3" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="4" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="5" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="6" class="java.lang.String" itemvalue="unidecode" />
            <item index="7" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="8" class="java.lang.String" itemvalue="torchvision" />
            <item index="9" class="java.lang.String" itemvalue="markupsafe" />
            <item index="10" class="java.lang.String" itemvalue="fsspec" />
            <item index="11" class="java.lang.String" itemvalue="appdirs" />
            <item index="12" class="java.lang.String" itemvalue="qudida" />
            <item index="13" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="14" class="java.lang.String" itemvalue="certifi" />
            <item index="15" class="java.lang.String" itemvalue="multiprocess" />
            <item index="16" class="java.lang.String" itemvalue="gitpython" />
            <item index="17" class="java.lang.String" itemvalue="pyparsing" />
            <item index="18" class="java.lang.String" itemvalue="sympy" />
            <item index="19" class="java.lang.String" itemvalue="tifffile" />
            <item index="20" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="21" class="java.lang.String" itemvalue="attrs" />
            <item index="22" class="java.lang.String" itemvalue="jinja2" />
            <item index="23" class="java.lang.String" itemvalue="fonttools" />
            <item index="24" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="25" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="26" class="java.lang.String" itemvalue="imageio" />
            <item index="27" class="java.lang.String" itemvalue="matplotlib" />
            <item index="28" class="java.lang.String" itemvalue="scikit-image" />
            <item index="29" class="java.lang.String" itemvalue="datasets" />
            <item index="30" class="java.lang.String" itemvalue="numpy" />
            <item index="31" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="32" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="33" class="java.lang.String" itemvalue="seaborn" />
            <item index="34" class="java.lang.String" itemvalue="zipp" />
            <item index="35" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="36" class="java.lang.String" itemvalue="wandb" />
            <item index="37" class="java.lang.String" itemvalue="urllib3" />
            <item index="38" class="java.lang.String" itemvalue="pyarrow" />
            <item index="39" class="java.lang.String" itemvalue="scipy" />
            <item index="40" class="java.lang.String" itemvalue="opencv-python" />
            <item index="41" class="java.lang.String" itemvalue="tzdata" />
            <item index="42" class="java.lang.String" itemvalue="dill" />
            <item index="43" class="java.lang.String" itemvalue="packaging" />
            <item index="44" class="java.lang.String" itemvalue="torch" />
            <item index="45" class="java.lang.String" itemvalue="lazy-loader" />
            <item index="46" class="java.lang.String" itemvalue="albumentations" />
            <item index="47" class="java.lang.String" itemvalue="pandas" />
            <item index="48" class="java.lang.String" itemvalue="tqdm" />
            <item index="49" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="50" class="java.lang.String" itemvalue="pillow" />
            <item index="51" class="java.lang.String" itemvalue="aiohttp" />
            <item index="52" class="java.lang.String" itemvalue="multidict" />
            <item index="53" class="java.lang.String" itemvalue="pytz" />
            <item index="54" class="java.lang.String" itemvalue="cnstd" />
            <item index="55" class="java.lang.String" itemvalue="cookiesparser" />
            <item index="56" class="java.lang.String" itemvalue="httpx" />
            <item index="57" class="java.lang.String" itemvalue="PyExecJS" />
            <item index="58" class="java.lang.String" itemvalue="win32_setctime" />
            <item index="59" class="java.lang.String" itemvalue="greenlet" />
            <item index="60" class="java.lang.String" itemvalue="nvidia-cufft-cu12" />
            <item index="61" class="java.lang.String" itemvalue="pycparser" />
            <item index="62" class="java.lang.String" itemvalue="gitdb" />
            <item index="63" class="java.lang.String" itemvalue="redis" />
            <item index="64" class="java.lang.String" itemvalue="langchain-community" />
            <item index="65" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="66" class="java.lang.String" itemvalue="aliyun-python-sdk-core" />
            <item index="67" class="java.lang.String" itemvalue="langchain" />
            <item index="68" class="java.lang.String" itemvalue="fake-useragent" />
            <item index="69" class="java.lang.String" itemvalue="fire" />
            <item index="70" class="java.lang.String" itemvalue="lxml" />
            <item index="71" class="java.lang.String" itemvalue="pyarrow-hotfix" />
            <item index="72" class="java.lang.String" itemvalue="pyreadline3" />
            <item index="73" class="java.lang.String" itemvalue="soupsieve" />
            <item index="74" class="java.lang.String" itemvalue="nvidia-cuda-runtime-cu12" />
            <item index="75" class="java.lang.String" itemvalue="PyPDF2" />
            <item index="76" class="java.lang.String" itemvalue="xlrd" />
            <item index="77" class="java.lang.String" itemvalue="GitPython" />
            <item index="78" class="java.lang.String" itemvalue="pydantic" />
            <item index="79" class="java.lang.String" itemvalue="transformers" />
            <item index="80" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="81" class="java.lang.String" itemvalue="aniso8601" />
            <item index="82" class="java.lang.String" itemvalue="PyMuPDFb" />
            <item index="83" class="java.lang.String" itemvalue="loguru" />
            <item index="84" class="java.lang.String" itemvalue="click" />
            <item index="85" class="java.lang.String" itemvalue="simplejson" />
            <item index="86" class="java.lang.String" itemvalue="openai" />
            <item index="87" class="java.lang.String" itemvalue="nvidia-cublas-cu12" />
            <item index="88" class="java.lang.String" itemvalue="pdfminer.six" />
            <item index="89" class="java.lang.String" itemvalue="regex" />
            <item index="90" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="91" class="java.lang.String" itemvalue="platformdirs" />
            <item index="92" class="java.lang.String" itemvalue="oss2" />
            <item index="93" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="94" class="java.lang.String" itemvalue="httpcore" />
            <item index="95" class="java.lang.String" itemvalue="idna" />
            <item index="96" class="java.lang.String" itemvalue="modelscope" />
            <item index="97" class="java.lang.String" itemvalue="PyJWT" />
            <item index="98" class="java.lang.String" itemvalue="langchain-openai" />
            <item index="99" class="java.lang.String" itemvalue="networkx" />
            <item index="100" class="java.lang.String" itemvalue="nvidia-nvjitlink-cu12" />
            <item index="101" class="java.lang.String" itemvalue="smmap" />
            <item index="102" class="java.lang.String" itemvalue="cffi" />
            <item index="103" class="java.lang.String" itemvalue="nvidia-cusparse-cu12" />
            <item index="104" class="java.lang.String" itemvalue="requests" />
            <item index="105" class="java.lang.String" itemvalue="sniffio" />
            <item index="106" class="java.lang.String" itemvalue="httpx-sse" />
            <item index="107" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="108" class="java.lang.String" itemvalue="mypy_extensions" />
            <item index="109" class="java.lang.String" itemvalue="XlsxWriter" />
            <item index="110" class="java.lang.String" itemvalue="celery" />
            <item index="111" class="java.lang.String" itemvalue="tenacity" />
            <item index="112" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="113" class="java.lang.String" itemvalue="blinker" />
            <item index="114" class="java.lang.String" itemvalue="annotated-types" />
            <item index="115" class="java.lang.String" itemvalue="PyMuPDF" />
            <item index="116" class="java.lang.String" itemvalue="paddlepaddle-gpu" />
            <item index="117" class="java.lang.String" itemvalue="amqp" />
            <item index="118" class="java.lang.String" itemvalue="nvidia-cudnn-cu12" />
            <item index="119" class="java.lang.String" itemvalue="et-xmlfile" />
            <item index="120" class="java.lang.String" itemvalue="ultralytics" />
            <item index="121" class="java.lang.String" itemvalue="addict" />
            <item index="122" class="java.lang.String" itemvalue="sortedcontainers" />
            <item index="123" class="java.lang.String" itemvalue="termcolor" />
            <item index="124" class="java.lang.String" itemvalue="pdfplumber" />
            <item index="125" class="java.lang.String" itemvalue="cnocr" />
            <item index="126" class="java.lang.String" itemvalue="mpmath" />
            <item index="127" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="128" class="java.lang.String" itemvalue="cachetools" />
            <item index="129" class="java.lang.String" itemvalue="DBUtils" />
            <item index="130" class="java.lang.String" itemvalue="langchain-core" />
            <item index="131" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="132" class="java.lang.String" itemvalue="yarl" />
            <item index="133" class="java.lang.String" itemvalue="click-repl" />
            <item index="134" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="135" class="java.lang.String" itemvalue="einops" />
            <item index="136" class="java.lang.String" itemvalue="setproctitle" />
            <item index="137" class="java.lang.String" itemvalue="joblib" />
            <item index="138" class="java.lang.String" itemvalue="jiter" />
            <item index="139" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="140" class="java.lang.String" itemvalue="html2text" />
            <item index="141" class="java.lang.String" itemvalue="gast" />
            <item index="142" class="java.lang.String" itemvalue="kombu" />
            <item index="143" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="144" class="java.lang.String" itemvalue="ultralytics-thop" />
            <item index="145" class="java.lang.String" itemvalue="frozenlist" />
            <item index="146" class="java.lang.String" itemvalue="nvidia-cusolver-cu12" />
            <item index="147" class="java.lang.String" itemvalue="nvidia-curand-cu12" />
            <item index="148" class="java.lang.String" itemvalue="filelock" />
            <item index="149" class="java.lang.String" itemvalue="safetensors" />
            <item index="150" class="java.lang.String" itemvalue="rapidocr-onnxruntime" />
            <item index="151" class="java.lang.String" itemvalue="anyio" />
            <item index="152" class="java.lang.String" itemvalue="elastic-transport" />
            <item index="153" class="java.lang.String" itemvalue="Markdown" />
            <item index="154" class="java.lang.String" itemvalue="vine" />
            <item index="155" class="java.lang.String" itemvalue="xxhash" />
            <item index="156" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="157" class="java.lang.String" itemvalue="tokenizers" />
            <item index="158" class="java.lang.String" itemvalue="dnspython" />
            <item index="159" class="java.lang.String" itemvalue="cryptography" />
            <item index="160" class="java.lang.String" itemvalue="orjson" />
            <item index="161" class="java.lang.String" itemvalue="python-pptx" />
            <item index="162" class="java.lang.String" itemvalue="coloredlogs" />
            <item index="163" class="java.lang.String" itemvalue="pdf2docx" />
            <item index="164" class="java.lang.String" itemvalue="argon2-cffi-bindings" />
            <item index="165" class="java.lang.String" itemvalue="langsmith" />
            <item index="166" class="java.lang.String" itemvalue="paddlex" />
            <item index="167" class="java.lang.String" itemvalue="environs" />
            <item index="168" class="java.lang.String" itemvalue="async-timeout" />
            <item index="169" class="java.lang.String" itemvalue="yapf" />
            <item index="170" class="java.lang.String" itemvalue="more-itertools" />
            <item index="171" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="172" class="java.lang.String" itemvalue="minio" />
            <item index="173" class="java.lang.String" itemvalue="crcmod" />
            <item index="174" class="java.lang.String" itemvalue="click-didyoumean" />
            <item index="175" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="176" class="java.lang.String" itemvalue="Jinja2" />
            <item index="177" class="java.lang.String" itemvalue="typeguard" />
            <item index="178" class="java.lang.String" itemvalue="typing-inspection" />
            <item index="179" class="java.lang.String" itemvalue="eventlet" />
            <item index="180" class="java.lang.String" itemvalue="pypdfium2" />
            <item index="181" class="java.lang.String" itemvalue="onnx" />
            <item index="182" class="java.lang.String" itemvalue="pymilvus" />
            <item index="183" class="java.lang.String" itemvalue="billiard" />
            <item index="184" class="java.lang.String" itemvalue="Flask" />
            <item index="185" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="186" class="java.lang.String" itemvalue="python-docx" />
            <item index="187" class="java.lang.String" itemvalue="six" />
            <item index="188" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="189" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="190" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="191" class="java.lang.String" itemvalue="readability-lxml" />
            <item index="192" class="java.lang.String" itemvalue="sqlacodegen" />
            <item index="193" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="194" class="java.lang.String" itemvalue="zstandard" />
            <item index="195" class="java.lang.String" itemvalue="jmespath" />
            <item index="196" class="java.lang.String" itemvalue="Unidecode" />
            <item index="197" class="java.lang.String" itemvalue="aliyun-python-sdk-kms" />
            <item index="198" class="java.lang.String" itemvalue="inflect" />
            <item index="199" class="java.lang.String" itemvalue="grpcio" />
            <item index="200" class="java.lang.String" itemvalue="langchain-text-splitters" />
            <item index="201" class="java.lang.String" itemvalue="click-plugins" />
            <item index="202" class="java.lang.String" itemvalue="aiosignal" />
            <item index="203" class="java.lang.String" itemvalue="Flask-RESTful" />
            <item index="204" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>
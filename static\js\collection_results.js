// 采集结果页面JavaScript

let currentPage = 1;
let currentFilters = {};
let selectedIds = [];
let pendingPublishStatus = '';
let currentNewspaperId = null;
let currentNewspaperName = '';

$(document).ready(function() {
    initFromUrlParams();
    loadCollectionResults();
    loadStatistics();
    initEventHandlers();
}

// 从URL参数初始化
function initFromUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const newspaperId = urlParams.get('newspaper_id');

    if (newspaperId) {
        currentNewspaperId = parseInt(newspaperId);

        // 获取报纸信息并设置筛选条件
        $.get(`/api/newspapers/${newspaperId}`)
            .done(function(response) {
                if (response.success) {
                    const newspaper = response.data;
                    currentNewspaperName = newspaper.name;

                    // 设置筛选条件
                    $('#filter-newspaper-name').val(newspaper.name);

                    // 更新页面标题，显示当前查看的报纸
                    updatePageTitle(newspaper.name);

                    // 添加返回按钮
                    addBackButton();

                    // 显示当前筛选状态
                    showCurrentFilter(newspaper.name);
                } else {
                    console.error('获取报纸信息失败:', response.message);
                }
            })
            .fail(function() {
                console.error('获取报纸信息失败');
            });
    }
}

// 更新页面标题
function updatePageTitle(newspaperName) {
    const pageTitle = $('h1.h3');
    pageTitle.html(`
        <i class="fas fa-database me-2"></i>采集结果
        <small class="text-muted ms-2">- ${newspaperName}</small>
    `);
}

// 添加返回按钮
function addBackButton() {
    const headerDiv = $('.d-flex.justify-content-between.align-items-center.mb-4');
    const backButton = `
        <button class="btn btn-outline-secondary me-2" onclick="goBackToNewspapers()">
            <i class="fas fa-arrow-left me-1"></i>返回报纸管理
        </button>
    `;
    headerDiv.find('div:last-child').prepend(backButton);
}

// 显示当前筛选状态
function showCurrentFilter(newspaperName) {
    const filterCard = $('.card.mb-4').first();
    const filterAlert = `
        <div class="alert alert-info mb-3" id="current-filter-alert">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-filter me-2"></i>
                    <strong>当前筛选：</strong>${newspaperName} 的采集结果
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="clearNewspaperFilter()">
                    <i class="fas fa-times me-1"></i>清除筛选
                </button>
            </div>
        </div>
    `;
    filterCard.before(filterAlert);
}

// 返回报纸管理页面
function goBackToNewspapers() {
    window.location.href = '/newspapers';
}

// 清除报纸筛选
function clearNewspaperFilter() {
    currentNewspaperId = null;
    currentNewspaperName = '';
    $('#filter-newspaper-name').val('');
    $('#current-filter-alert').remove();

    // 更新页面标题
    $('h1.h3').html('<i class="fas fa-database me-2"></i>采集结果');

    // 移除返回按钮
    $('.btn-outline-secondary:contains("返回报纸管理")').remove();

    // 重新加载数据
    currentPage = 1;
    loadCollectionResults();

    // 更新URL，移除newspaper_id参数
    const url = new URL(window.location);
    url.searchParams.delete('newspaper_id');
    window.history.replaceState({}, '', url);
});

// 初始化事件处理器
function initEventHandlers() {
    // 筛选表单提交
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadCollectionResults();
    });
    
    // 全选/取消全选
    $('#select-all').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.row-checkbox').prop('checked', isChecked);
        updateSelectedCount();
    });
    
    // 行选择
    $(document).on('change', '.row-checkbox', function() {
        updateSelectedCount();
        updateSelectAllState();
    });
    
    // 设置默认日期为今天
    $('#filter-collection-date').val(new Date().toISOString().split('T')[0]);
}

// 加载采集结果列表
function loadCollectionResults() {
    // 获取筛选条件
    currentFilters = {
        page: currentPage,
        per_page: 20,
        newspaper_name: $('#filter-newspaper-name').val(),
        province: $('#filter-province').val(),
        city: $('#filter-city').val(),
        collection_date: $('#filter-collection-date').val(),
        publish_status: $('#filter-publish-status').val()
    };

    // 如果有指定的报纸ID，添加到筛选条件
    if (currentNewspaperId) {
        currentFilters.newspaper_id = currentNewspaperId;
    }
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key] === '') {
            delete currentFilters[key];
        }
    });
    
    showLoading('#results-table');
    
    $.get('/api/collection/results', currentFilters)
        .done(function(response) {
            if (response.success) {
                renderResultsTable(response.data);
                renderPagination(response.pagination);
            } else {
                showMessage(response.message, 'danger');
            }
        })
        .fail(function() {
            $('#results-table').html('<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 加载统计信息
function loadStatistics() {
    // 这里可以添加专门的统计API，暂时使用模拟数据
    $('#total-results').text('0');
    $('#published-results').text('0');
    $('#unpublished-results').text('0');
    $('#today-results').text('0');
}

// 渲染结果表格
function renderResultsTable(results) {
    const tbody = $('#results-table');
    tbody.empty();
    
    if (results.length === 0) {
        tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无数据</td></tr>');
        return;
    }
    
    results.forEach(function(result) {
        const newsLinks = result.news_links ? JSON.parse(result.news_links) : [];
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input row-checkbox" value="${result.id}">
                </td>
                <td>
                    <div class="fw-bold">${result.newspaper_name}</div>
                    <small class="text-muted">${result.province || ''} ${result.city || ''}</small>
                </td>
                <td>
                    <div class="fw-bold">${result.page_title || '未知标题'}</div>
                    <small class="text-muted">
                        <a href="${result.page_link}" target="_blank" class="text-decoration-none">
                            <i class="fas fa-external-link-alt me-1"></i>查看版面
                        </a>
                    </small>
                </td>
                <td>
                    <div class="small">
                        ${result.image_link ? '<i class="fas fa-image text-success me-1"></i>图片' : '<i class="fas fa-image text-muted me-1"></i>无图片'}
                        ${result.pdf_link ? '<i class="fas fa-file-pdf text-danger me-1 ms-2"></i>PDF' : '<i class="fas fa-file-pdf text-muted me-1 ms-2"></i>无PDF'}
                    </div>
                    <div class="small mt-1">
                        <i class="fas fa-newspaper text-info me-1"></i>新闻: ${newsLinks.length} 条
                    </div>
                </td>
                <td>
                    <div>${formatDateTime(result.collection_time)}</div>
                    <small class="text-muted">${formatDate(result.collection_date)}</small>
                </td>
                <td>${getStatusBadge(result.publish_status, 'publish')}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-info" onclick="viewDetail(${result.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-${result.publish_status === '已发布' ? 'warning' : 'success'}" 
                                onclick="togglePublishStatus(${result.id}, '${result.publish_status}')" 
                                title="${result.publish_status === '已发布' ? '取消发布' : '发布'}">
                            <i class="fas fa-${result.publish_status === '已发布' ? 'download' : 'upload'}"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteResult(${result.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 渲染分页
function renderPagination(pagination) {
    const paginationHtml = generatePagination(pagination, 'goToPage');
    $('#pagination-container').html(paginationHtml);
}

// 跳转到指定页面
function goToPage(page) {
    currentPage = page;
    loadCollectionResults();
}

// 更新选中数量
function updateSelectedCount() {
    selectedIds = $('.row-checkbox:checked').map(function() {
        return parseInt($(this).val());
    }).get();
    $('#selected-count').text(selectedIds.length);
}

// 更新全选状态
function updateSelectAllState() {
    const totalCheckboxes = $('.row-checkbox').length;
    const checkedCheckboxes = $('.row-checkbox:checked').length;
    
    if (checkedCheckboxes === 0) {
        $('#select-all').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all').prop('indeterminate', true);
    }
}

// 重置筛选条件
function resetFilters() {
    $('#filter-form')[0].reset();
    $('#filter-collection-date').val(new Date().toISOString().split('T')[0]);
    currentPage = 1;
    loadCollectionResults();
}

// 查看详情
function viewDetail(id) {
    $.get(`/api/collection/results/${id}`)
        .done(function(response) {
            if (response.success) {
                showDetailModal(response.data);
            } else {
                showMessage(response.message, 'danger');
            }
        });
}

// 显示详情模态框
function showDetailModal(data) {
    const newsLinks = data.news_links ? JSON.parse(data.news_links) : [];
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>报纸信息</h6>
                <table class="table table-sm">
                    <tr><td>报纸名称:</td><td>${data.newspaper_name}</td></tr>
                    <tr><td>省市:</td><td>${data.province || ''} ${data.city || ''}</td></tr>
                    <tr><td>采集时间:</td><td>${formatDateTime(data.collection_time)}</td></tr>
                    <tr><td>发布状态:</td><td>${getStatusBadge(data.publish_status, 'publish')}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>版面信息</h6>
                <table class="table table-sm">
                    <tr><td>版面标题:</td><td>${data.page_title || '未知'}</td></tr>
                    <tr><td>版面链接:</td><td><a href="${data.page_link}" target="_blank" class="text-decoration-none">查看版面</a></td></tr>
                    <tr><td>新闻数量:</td><td>${newsLinks.length} 条</td></tr>
                </table>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>采集内容</h6>
            <div class="row">
                <div class="col-md-6">
                    <strong>版面图片:</strong>
                    ${data.image_link ? 
                        `<div class="mt-2">
                            <a href="${data.image_link}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>查看图片
                            </a>
                        </div>` : 
                        '<div class="text-muted mt-2">无图片链接</div>'
                    }
                </div>
                <div class="col-md-6">
                    <strong>版面PDF:</strong>
                    ${data.pdf_link ? 
                        `<div class="mt-2">
                            <a href="${data.pdf_link}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>查看PDF
                            </a>
                        </div>` : 
                        '<div class="text-muted mt-2">无PDF链接</div>'
                    }
                </div>
            </div>
        </div>
        
        ${newsLinks.length > 0 ? `
            <div class="mt-3">
                <h6>新闻链接 (${newsLinks.length} 条)</h6>
                <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                    ${newsLinks.map((link, index) => `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>新闻 ${index + 1}</span>
                            <a href="${link}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    `).join('')}
                </div>
            </div>
        ` : ''}
    `;
    
    $('#detail-content').html(html);
    $('#detailModal').modal('show');
}

// 切换发布状态
function togglePublishStatus(id, currentStatus) {
    const newStatus = currentStatus === '已发布' ? '未发布' : '已发布';
    
    $.ajax({
        url: `/api/collection/results/${id}/publish`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({publish_status: newStatus}),
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                loadCollectionResults(); // 刷新列表
            } else {
                showMessage(response.message, 'danger');
            }
        }
    });
}

// 删除结果
function deleteResult(id) {
    if (confirm('确定要删除这条采集结果吗？此操作不可撤销。')) {
        // 这里需要添加删除API
        showMessage('删除功能待实现', 'info');
    }
}

// 批量发布
function batchPublish() {
    if (selectedIds.length === 0) {
        showMessage('请先选择要发布的结果', 'warning');
        return;
    }
    
    pendingPublishStatus = '已发布';
    $('#publishModal .modal-body p').text(`确定要将选中的 ${selectedIds.length} 条结果标记为已发布吗？`);
    $('#publishModal').modal('show');
}

// 批量取消发布
function batchUnpublish() {
    if (selectedIds.length === 0) {
        showMessage('请先选择要取消发布的结果', 'warning');
        return;
    }
    
    pendingPublishStatus = '未发布';
    $('#publishModal .modal-body p').text(`确定要将选中的 ${selectedIds.length} 条结果标记为未发布吗？`);
    $('#publishModal').modal('show');
}

// 确认发布状态更新
function confirmPublishUpdate() {
    const promises = selectedIds.map(id => {
        return $.ajax({
            url: `/api/collection/results/${id}/publish`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({publish_status: pendingPublishStatus})
        });
    });
    
    Promise.all(promises)
        .then(function() {
            $('#publishModal').modal('hide');
            showMessage(`成功更新 ${selectedIds.length} 条结果的发布状态`, 'success');
            loadCollectionResults(); // 刷新列表
        })
        .catch(function() {
            $('#publishModal').modal('hide');
            showMessage('批量更新失败', 'danger');
        });
}

// 批量删除
function batchDelete() {
    if (selectedIds.length === 0) {
        showMessage('请先选择要删除的结果', 'warning');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedIds.length} 条采集结果吗？此操作不可撤销。`)) {
        showMessage('批量删除功能待实现', 'info');
    }
}

// 导出结果
function exportResults() {
    showMessage('导出功能待实现', 'info');
}

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试下拉框修复效果
"""
import requests
import json

def test_dropdown_fix():
    """测试下拉框修复效果"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔧 测试下拉框修复效果")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查修复后的HTML元素
            content = response.text
            
            # 检查是否移除了自动触发的data-bs-toggle
            if 'data-bs-toggle="dropdown"' in content:
                print("   ❌ 仍然包含自动触发的data-bs-toggle")
            else:
                print("   ✅ 已移除自动触发的data-bs-toggle")
            
            # 检查新的事件处理函数
            new_handlers = [
                'handlePageLinkRuleClick',
                'handleImageLinkRuleClick', 
                'handlePdfLinkRuleClick',
                'handleNewsLinkRuleClick',
                'handlePageLinkRuleFocus',
                'handleImageLinkRuleFocus',
                'handlePdfLinkRuleFocus',
                'handleNewsLinkRuleFocus'
            ]
            
            print("   🔍 检查新的事件处理函数:")
            for handler in new_handlers:
                if handler in content:
                    print(f"   ✅ {handler}")
                else:
                    print(f"   ❌ {handler} 缺失")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript修复
    print("\n2️⃣ 测试JavaScript修复...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查修复相关的函数和逻辑
            fixes = [
                ('initDropdownComponents', '下拉框组件初始化函数'),
                ('bootstrap.Dropdown.getInstance', 'Bootstrap实例获取'),
                ('setTimeout', '延迟执行优化'),
                ('stopPropagation', '事件冒泡阻止'),
                ('handlePageLinkRuleClick', '点击事件处理'),
                ('handlePageLinkRuleFocus', '焦点事件处理')
            ]
            
            print("   🔧 检查修复点:")
            for fix_code, description in fixes:
                if fix_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
            # 检查延迟时间设置
            if 'setTimeout(() => {' in js_content:
                print("   ✅ 使用setTimeout延迟执行")
            else:
                print("   ❌ 未使用setTimeout延迟执行")
                
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
                print(f"   🖼️ 图片链接规则: {len(data.get('image_link_rules', []))} 个")
                print(f"   📋 PDF链接规则: {len(data.get('pdf_link_rules', []))} 个")
                print(f"   📰 新闻链接规则: {len(data.get('news_link_rules', []))} 个")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 下拉框修复测试完成！")
    
    print("\n📋 修复内容总结:")
    print("✅ 移除自动触发 - 移除data-bs-toggle属性")
    print("✅ 手动控制 - 使用JavaScript手动控制下拉框")
    print("✅ 延迟执行 - 使用setTimeout避免事件冲突")
    print("✅ 实例管理 - 正确获取和管理Bootstrap实例")
    print("✅ 事件优化 - 分离点击和焦点事件处理")
    print("✅ 状态检查 - 检查下拉框显示状态")
    
    print("\n🚀 预期效果:")
    print("1. 第一次点击筛选框时下拉框正常显示")
    print("2. 不会出现一闪就消失的问题")
    print("3. 后续点击行为保持正常")
    print("4. 筛选功能正常工作")
    print("5. 选择规则后正确填充")
    
    print("\n💡 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 点击任意筛选框，观察下拉框是否正常显示")
    print("3. 输入关键词测试筛选功能")
    print("4. 选择规则测试自动填充功能")
    
    return True

if __name__ == "__main__":
    test_dropdown_fix()

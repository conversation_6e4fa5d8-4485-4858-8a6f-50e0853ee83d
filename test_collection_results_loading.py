#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试采集结果页面加载问题
"""
import requests
import json

def test_collection_results_loading():
    """测试采集结果页面加载问题"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔄 测试采集结果页面加载问题")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/collection_results", timeout=10)
        if response.status_code == 200:
            print("   ✅ 采集结果页面可访问")
            
            # 检查JavaScript函数
            content = response.text
            
            required_functions = [
                ('showLoading', '加载状态显示函数'),
                ('showMessage', '消息显示函数'),
                ('formatDateTime', '日期时间格式化函数'),
                ('formatDate', '日期格式化函数'),
                ('getStatusBadge', '状态徽章函数'),
                ('generatePagination', '分页生成函数'),
                ('goToPage', '页面跳转函数'),
                ('loadCollectionResults', '加载采集结果函数'),
                ('renderResultsTable', '渲染表格函数'),
                ('renderPagination', '渲染分页函数')
            ]
            
            print("   🔍 检查必需的JavaScript函数:")
            missing_functions = []
            for func_name, description in required_functions:
                if func_name in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    missing_functions.append(func_name)
            
            if missing_functions:
                print(f"   ⚠️ 缺失函数: {', '.join(missing_functions)}")
            else:
                print("   ✅ 所有必需函数都存在")
                
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试API响应
    print("\n2️⃣ 测试API响应...")
    try:
        response = requests.get(f"{base_url}/api/collection/results", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ API响应正常")
            print(f"   📊 success: {result.get('success')}")
            
            if result.get('success'):
                data = result.get('data', [])
                pagination = result.get('pagination', {})
                print(f"   📄 数据条数: {len(data)}")
                print(f"   📖 分页信息: 第{pagination.get('page', 1)}页，共{pagination.get('pages', 1)}页")
                
                # 检查数据结构
                if data:
                    sample = data[0]
                    required_fields = ['id', 'newspaper_name', 'title', 'collection_date', 'publish_status']
                    print("   🔍 检查数据字段:")
                    for field in required_fields:
                        if field in sample:
                            print(f"   ✅ {field}: {sample.get(field)}")
                        else:
                            print(f"   ❌ {field}: 缺失")
                else:
                    print("   ℹ️ 暂无采集结果数据")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            print(f"   📝 响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
    
    # 3. 测试JavaScript文件
    print("\n3️⃣ 测试JavaScript文件...")
    try:
        response = requests.get(f"{base_url}/static/js/collection_results.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            print("   ✅ JavaScript文件可访问")
            
            # 检查关键代码片段
            code_checks = [
                ('showLoading(\'#results-table tbody\')', '加载状态调用'),
                ('$.get(\'/api/collection/results\'', 'API请求'),
                ('renderResultsTable(data)', '表格渲染调用'),
                ('renderPagination(pagination)', '分页渲染调用'),
                ('spinner-border', '加载动画'),
                ('alert alert-', '消息提示'),
                ('formatDateTime(', '日期格式化调用'),
                ('getStatusBadge(', '状态徽章调用')
            ]
            
            print("   🔍 检查关键代码片段:")
            for code, description in code_checks:
                if code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ JavaScript文件测试失败: {e}")
    
    # 4. 测试HTML结构
    print("\n4️⃣ 测试HTML结构...")
    try:
        response = requests.get(f"{base_url}/collection_results", timeout=10)
        if response.status_code == 200:
            html_content = response.text
            
            # 检查必需的HTML元素
            html_elements = [
                ('id="results-table"', '结果表格'),
                ('tbody', '表格主体'),
                ('id="pagination-container"', '分页容器'),
                ('collection_results.js', 'JavaScript文件引用'),
                ('$(document).ready', 'jQuery初始化'),
                ('loadCollectionResults', '加载函数调用')
            ]
            
            print("   🔍 检查HTML结构:")
            for element, description in html_elements:
                if element in html_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ HTML结构检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ HTML结构测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 采集结果页面加载问题诊断完成！")
    
    print("\n📋 修复的问题:")
    print("✅ 添加了 showLoading 函数 - 显示加载状态")
    print("✅ 添加了 showMessage 函数 - 显示消息提示")
    print("✅ 添加了 formatDateTime 函数 - 格式化日期时间")
    print("✅ 添加了 formatDate 函数 - 格式化日期")
    print("✅ 添加了 getStatusBadge 函数 - 生成状态徽章")
    print("✅ 添加了 generatePagination 函数 - 生成分页HTML")
    
    print("\n🔧 加载流程:")
    print("1. 页面加载 → $(document).ready()")
    print("2. 初始化 → initFromUrlParams() + loadCollectionResults()")
    print("3. 显示加载状态 → showLoading('#results-table tbody')")
    print("4. 请求API → $.get('/api/collection/results')")
    print("5. 渲染数据 → renderResultsTable() + renderPagination()")
    print("6. 完成加载 → 隐藏加载状态，显示数据")
    
    print("\n💡 如果仍有问题:")
    print("1. 清除浏览器缓存 (Ctrl+F5)")
    print("2. 检查浏览器控制台是否有JavaScript错误")
    print("3. 检查网络面板API请求是否正常")
    print("4. 确认数据库中有采集结果数据")
    
    print("\n🚀 测试建议:")
    print("1. 访问 http://127.0.0.1:5009/collection_results")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 查看控制台是否有错误信息")
    print("4. 查看网络面板API请求状态")
    print("5. 如果仍然转圈，检查具体的错误信息")
    
    return True

if __name__ == "__main__":
    test_collection_results_loading()

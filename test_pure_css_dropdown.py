#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试纯CSS下拉框实现
"""
import requests
import json

def test_pure_css_dropdown():
    """测试纯CSS下拉框实现"""
    base_url = "http://127.0.0.1:5009"
    
    print("🎨 测试纯CSS下拉框实现")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查HTML结构变化
            content = response.text
            
            # 检查是否移除了Bootstrap下拉框
            if 'class="dropdown"' in content:
                print("   ⚠️ 仍然包含Bootstrap下拉框类")
            else:
                print("   ✅ 已移除Bootstrap下拉框类")
            
            # 检查新的CSS结构
            if 'class="position-relative"' in content:
                print("   ✅ 使用position-relative布局")
            else:
                print("   ❌ 缺少position-relative布局")
            
            # 检查新的事件处理函数
            new_functions = [
                'togglePageLinkRules',
                'showPageLinkRulesOnFocus',
                'toggleImageLinkRules',
                'togglePdfLinkRules',
                'toggleNewsLinkRules'
            ]
            
            print("   🔍 检查新的事件处理函数:")
            for func in new_functions:
                if func in content:
                    print(f"   ✅ {func}")
                else:
                    print(f"   ❌ {func} 缺失")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript实现
    print("\n2️⃣ 测试JavaScript实现...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查纯CSS实现的关键特征
            css_features = [
                ('dropdown.show()', 'jQuery显示方法'),
                ('dropdown.hide()', 'jQuery隐藏方法'),
                ('togglePageLinkRules', '切换函数'),
                ('showPageLinkRulesOnFocus', '焦点显示函数'),
                ('window.dropdownStates', '状态跟踪'),
                ('position-relative', '相对定位'),
                ('display: none', '初始隐藏状态')
            ]
            
            print("   🎨 检查纯CSS实现特征:")
            for feature_code, description in css_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
            
            # 检查是否移除了Bootstrap依赖
            bootstrap_features = [
                'bootstrap.Dropdown.getInstance',
                'bootstrap.Dropdown',
                'data-bs-toggle'
            ]
            
            print("   🚫 检查Bootstrap依赖移除:")
            for feature in bootstrap_features:
                if feature in js_content:
                    print(f"   ⚠️ 仍然包含 {feature}")
                else:
                    print(f"   ✅ 已移除 {feature}")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 纯CSS下拉框测试完成！")
    
    print("\n📋 实现方案总结:")
    print("✅ 移除Bootstrap依赖 - 不再使用Bootstrap下拉框组件")
    print("✅ 纯CSS控制 - 使用jQuery的show()/hide()方法")
    print("✅ 简化HTML结构 - 使用position-relative布局")
    print("✅ 状态跟踪优化 - 使用JavaScript变量跟踪状态")
    print("✅ 事件处理简化 - 直接控制显示/隐藏，无需复杂的实例管理")
    
    print("\n🔄 预期行为:")
    print("1. 第一次点击输入框 → 下拉框正常显示（不会一闪消失）")
    print("2. 再次点击输入框 → 下拉框正常隐藏")
    print("3. 输入筛选关键词 → 下拉框保持显示并更新内容")
    print("4. 选择规则 → 自动填充并隐藏下拉框")
    print("5. 点击页面其他地方 → 隐藏所有下拉框")
    
    print("\n💡 技术优势:")
    print("- 完全避免Bootstrap事件冲突")
    print("- 更简单的状态管理")
    print("- 更直接的DOM操作")
    print("- 更好的控制性和可预测性")
    
    print("\n🚀 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 点击版面链接筛选框，观察下拉框显示")
    print("3. 再次点击，观察下拉框隐藏")
    print("4. 输入关键词测试筛选功能")
    print("5. 选择规则测试自动填充")
    print("6. 测试其他筛选框的功能")
    
    return True

if __name__ == "__main__":
    test_pure_css_dropdown()

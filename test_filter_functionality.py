#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试筛选功能
"""
import requests
import json

def test_filter_functionality():
    """测试筛选功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔍 测试规则筛选功能")
    print("=" * 60)
    
    # 1. 测试API是否正常
    print("1️⃣ 测试提取规则API...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                
                print(f"   ✅ API正常")
                print(f"   📄 版面链接规则数量: {len(data.get('page_link_rules', []))}")
                print(f"   🖼️ 图片链接规则数量: {len(data.get('image_link_rules', []))}")
                print(f"   📋 PDF链接规则数量: {len(data.get('pdf_link_rules', []))}")
                print(f"   📰 新闻链接规则数量: {len(data.get('news_link_rules', []))}")
                
                # 显示规则内容，用于测试筛选
                print("\n   📋 可用于筛选测试的规则示例:")
                if data.get('page_link_rules'):
                    for i, rule in enumerate(data['page_link_rules'][:3], 1):
                        print(f"   {i}. {rule['rule'][:50]}{'...' if len(rule['rule']) > 50 else ''}")
                        print(f"      方法: {rule['method']}, 来源: {rule['newspaper_name']}")
                
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
        return False
    
    # 2. 测试页面访问
    print("\n2️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查新的筛选元素
            content = response.text
            filter_elements = [
                ('page_link_rule_filter', '版面链接规则筛选框'),
                ('image_link_rule_filter', '图片链接规则筛选框'),
                ('pdf_link_rule_filter', 'PDF链接规则筛选框'),
                ('news_link_rule_filter', '新闻链接规则筛选框'),
                ('page_link_rule_dropdown', '版面链接规则下拉框'),
                ('image_link_rule_dropdown', '图片链接规则下拉框'),
                ('pdf_link_rule_dropdown', 'PDF链接规则下拉框'),
                ('news_link_rule_dropdown', '新闻链接规则下拉框')
            ]
            
            print("   🔍 检查筛选元素:")
            for element_id, description in filter_elements:
                if element_id in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 3. 测试JavaScript筛选函数
    print("\n3️⃣ 测试JavaScript筛选函数...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查筛选相关函数
            filter_functions = [
                'filterPageLinkRules',
                'filterImageLinkRules', 
                'filterPdfLinkRules',
                'filterNewsLinkRules',
                'populatePageLinkRules',
                'populateImageLinkRules',
                'populatePdfLinkRules',
                'populateNewsLinkRules',
                'selectPageLinkRuleByIndex',
                'selectImageLinkRuleByIndex',
                'selectPdfLinkRuleByIndex',
                'selectNewsLinkRuleByIndex'
            ]
            
            print("   🔧 检查筛选函数:")
            for func in filter_functions:
                if func in js_content:
                    print(f"   ✅ 函数 {func}")
                else:
                    print(f"   ❌ 函数 {func} 缺失")
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 筛选功能测试完成！")
    
    print("\n📋 新功能特点:")
    print("✅ 输入筛选 - 支持关键词筛选规则")
    print("✅ 多字段匹配 - 规则内容、方法、来源报纸都可筛选")
    print("✅ 实时筛选 - 输入时实时更新筛选结果")
    print("✅ 美观界面 - 使用Bootstrap下拉框组件")
    print("✅ 完整显示 - 规则内容完整传递，不会截断")
    
    print("\n🚀 使用方法:")
    print("1. 点击筛选框或输入关键词")
    print("2. 系统会实时显示匹配的规则")
    print("3. 点击选择规则会自动填充到文本框")
    print("4. 支持按规则内容、方法、来源报纸筛选")
    
    print("\n💡 筛选示例:")
    print("- 输入 'xpath' 筛选XPath规则")
    print("- 输入 'div' 筛选包含div的规则")
    print("- 输入 '人民日报' 筛选来自人民日报的规则")
    print("- 输入 'swiper' 筛选包含swiper的规则")
    
    return True

if __name__ == "__main__":
    test_filter_functionality()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试数据库排序
"""
import requests

def test_direct_sorting():
    """直接测试数据库排序"""
    print('直接测试数据库排序:')
    
    # 测试不同的排序参数
    test_cases = [
        ('默认（无参数）', ''),
        ('创建时间倒序', '?sort_by=created_at&sort_order=desc'),
        ('创建时间正序', '?sort_by=created_at&sort_order=asc'),
        ('名称A-Z', '?sort_by=name&sort_order=asc'),
        ('名称Z-A', '?sort_by=name&sort_order=desc'),
    ]
    
    for test_name, params in test_cases:
        print(f'\n{test_name}:')
        url = f'http://127.0.0.1:5009/api/newspapers{params}'
        print(f'URL: {url}')
        
        try:
            r = requests.get(url)
            if r.status_code == 200:
                data = r.json()
                if data['success']:
                    newspapers = data['data']
                    print(f'返回 {len(newspapers)} 个报纸:')
                    for i, n in enumerate(newspapers, 1):
                        name = n['name']
                        newspaper_id = n['id']
                        created_at = n['created_at']
                        print(f'  {i}. {name} (ID: {newspaper_id}) - {created_at}')
                else:
                    print(f'API错误: {data.get("message")}')
            else:
                print(f'HTTP错误: {r.status_code}')
        except Exception as e:
            print(f'请求失败: {e}')
    
    print('\n分析结果:')
    print('如果所有测试都返回相同的顺序，说明排序没有生效')
    print('如果不同参数返回不同顺序，说明排序正常工作')

if __name__ == "__main__":
    test_direct_sorting()

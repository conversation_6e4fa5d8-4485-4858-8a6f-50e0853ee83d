#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新增的API接口
"""
import requests
import json

def test_api():
    base_url = "http://127.0.0.1:5009"
    
    # 测试数据 - 使用httpbin.org作为测试URL
    test_data = {
        "name": "测试报纸",
        "url": "http://httpbin.org/html",
        "page_link_rule": "//a/@href",
        "page_link_method": "xpath"
    }
    
    print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
    print("\n" + "="*50)
    
    # 测试原有的page_links接口
    print("1. 测试原有的page_links接口...")
    try:
        response = requests.post(
            f"{base_url}/api/test/page_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("响应:", json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("响应内容:", response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "="*50)
    
    # 测试新的two_links接口
    print("2. 测试新的two_links接口...")
    try:
        response = requests.post(
            f"{base_url}/api/test/two_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("响应:", json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("响应内容:", response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "="*50)
    
    # 测试新的three_links接口
    print("3. 测试新的three_links接口...")
    try:
        response = requests.post(
            f"{base_url}/api/test/three_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("响应:", json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("响应内容:", response.text)
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_api()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试正则表达式修复
"""
import requests
import json

def test_regex_fix():
    """测试正则表达式修复"""
    base_url = "http://127.0.0.1:5009"
    
    # 测试数据 - 使用包含多个捕获组的正则表达式
    test_data = {
        "name": "测试报纸",
        "url": "http://httpbin.org/html",
        "page_link_rule": "<img class=\"preview\" src=\"(.*?)\".*?<a href=\"(.*?)\"",
        "page_link_method": "re"
    }
    
    print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
    print("\n" + "="*60)
    
    # 测试三链接提取
    print("测试三链接提取（多捕获组正则表达式）...")
    try:
        response = requests.post(
            f"{base_url}/api/test/three_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("响应:", json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("响应内容:", response.text)
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_regex_fix()

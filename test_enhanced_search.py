#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试增强的搜索筛选功能
"""
import requests
import json

def test_enhanced_search():
    """测试增强的搜索筛选功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔍 测试增强的搜索筛选功能")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查增强搜索功能
            content = response.text
            
            enhanced_features = [
                ('handlePageLinkRuleInput', '增强输入处理'),
                ('isCompleteRuleInput', '规则检测函数'),
                ('isSimilarSearch', '相似搜索参数'),
                ('或输入关键词查看智能提示', '新的提示文本')
            ]
            
            print("   🔍 检查增强搜索功能:")
            for feature, description in enhanced_features:
                if feature in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript搜索算法
    print("\n2️⃣ 测试JavaScript搜索算法...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查高级搜索算法
            search_features = [
                ('searchRules', '高级搜索函数'),
                ('calculateMatchScore', '匹配度计算'),
                ('highlightMatches', '关键词高亮'),
                ('escapeRegExp', '正则转义'),
                ('queryWords', '关键词分割'),
                ('lengthSimilarity', '长度相似性'),
                ('scoredRules.sort', '结果排序'),
                ('background-color: #ffeb3b', '高亮样式'),
                ('badge bg-', '匹配度徽章')
            ]
            
            print("   🧠 检查搜索算法特征:")
            for feature_code, description in search_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
            
            # 检查智能检测模式
            detection_features = [
                ('isCompleteRuleInput', '完整规则检测'),
                ('rulePatterns', '规则模式数组'),
                ('contains', 'XPath函数检测'),
                ('class=', 'CSS类检测'),
                ('href', '链接属性检测')
            ]
            
            print("   🎯 检查智能检测:")
            for feature, description in detection_features:
                if feature in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API数据
    print("\n3️⃣ 测试API数据...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                
                # 分析规则数据，用于测试搜索功能
                rule_types = ['page_link_rules', 'image_link_rules', 'pdf_link_rules', 'news_link_rules']
                total_rules = 0
                
                for rule_type in rule_types:
                    rules = data.get(rule_type, [])
                    count = len(rules)
                    total_rules += count
                    type_name = rule_type.replace('_rules', '').replace('_', ' ').title()
                    print(f"   📄 {type_name}: {count} 个")
                
                print(f"   📊 总计: {total_rules} 个规则可供搜索")
                
                # 显示一些搜索测试用例
                if data.get('page_link_rules'):
                    print("   🔍 搜索测试建议:")
                    sample_rules = data['page_link_rules'][:3]
                    for i, rule in enumerate(sample_rules, 1):
                        # 提取可能的搜索关键词
                        rule_text = rule['rule']
                        keywords = []
                        if 'div' in rule_text.lower():
                            keywords.append('div')
                        if 'class' in rule_text.lower():
                            keywords.append('class')
                        if 'href' in rule_text.lower():
                            keywords.append('href')
                        if keywords:
                            print(f"   {i}. 尝试搜索: {', '.join(keywords[:2])}")
                        
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 增强搜索功能测试完成！")
    
    print("\n📋 增强搜索特性:")
    print("✅ 实时搜索 - 每次输入都触发搜索")
    print("✅ 智能检测 - 自动识别规则输入vs关键词搜索")
    print("✅ 多字段匹配 - 搜索规则内容、方法、报纸名称")
    print("✅ 匹配度评分 - 显示匹配度百分比")
    print("✅ 关键词高亮 - 突出显示匹配的文本")
    print("✅ 智能排序 - 按相关性排序结果")
    print("✅ 相似搜索 - 为完整规则提供相似建议")
    
    print("\n🎯 搜索算法特点:")
    print("- 完全匹配: 50分基础分")
    print("- 单词匹配: 每个词20分")
    print("- 开头匹配: 额外10分加成")
    print("- 长度相似性: 最多10分")
    print("- 最终分数: 0-100分")
    
    print("\n🔍 搜索模式:")
    print("1. 关键词搜索 - 输入 'div' 查看包含div的规则")
    print("2. 方法搜索 - 输入 'xpath' 查看XPath规则")
    print("3. 报纸搜索 - 输入报纸名称查看相关规则")
    print("4. 相似搜索 - 输入完整规则查看相似规则")
    
    print("\n🎨 视觉增强:")
    print("- 黄色高亮: 完全匹配的关键词")
    print("- 橙色高亮: 部分匹配的关键词")
    print("- 绿色徽章: 高匹配度 (80%+)")
    print("- 黄色徽章: 中匹配度 (60-80%)")
    print("- 灰色徽章: 低匹配度 (<60%)")
    
    print("\n🚀 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 在版面链接规则框中输入 'div'")
    print("3. 观察实时搜索结果和匹配度评分")
    print("4. 尝试输入 'xpath' 或 'class' 等关键词")
    print("5. 输入完整规则测试相似搜索")
    print("6. 观察关键词高亮效果")
    
    return True

if __name__ == "__main__":
    test_enhanced_search()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试下拉框切换功能
"""
import requests
import json

def test_toggle_functionality():
    """测试下拉框切换功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔄 测试下拉框切换功能")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查切换相关的HTML元素
            content = response.text
            
            # 检查点击事件处理函数
            click_handlers = [
                'handlePageLinkRuleClick',
                'handleImageLinkRuleClick', 
                'handlePdfLinkRuleClick',
                'handleNewsLinkRuleClick'
            ]
            
            print("   🔍 检查点击事件处理函数:")
            for handler in click_handlers:
                if handler in content:
                    print(f"   ✅ {handler}")
                else:
                    print(f"   ❌ {handler} 缺失")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript切换逻辑
    print("\n2️⃣ 测试JavaScript切换逻辑...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查切换相关的函数和逻辑
            toggle_features = [
                ('hasClass(\'show\')', '显示状态检查'),
                ('hidePageLinkRules', '版面链接隐藏函数'),
                ('hideImageLinkRules', '图片链接隐藏函数'),
                ('hidePdfLinkRules', 'PDF链接隐藏函数'),
                ('hideNewsLinkRules', '新闻链接隐藏函数'),
                ('dropdown.hide()', '下拉框隐藏方法'),
                ('if (isVisible)', '可见性判断逻辑'),
                ('} else {', '显示/隐藏分支逻辑')
            ]
            
            print("   🔧 检查切换功能:")
            for feature_code, description in toggle_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
            # 检查切换逻辑的完整性
            if 'if (isVisible)' in js_content and 'dropdown.hide()' in js_content:
                print("   ✅ 切换逻辑完整")
            else:
                print("   ❌ 切换逻辑不完整")
                
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
                
                # 显示一些规则示例，用于测试切换功能
                if data.get('page_link_rules'):
                    print("   📋 可用于测试的规则示例:")
                    for i, rule in enumerate(data['page_link_rules'][:2], 1):
                        rule_preview = rule['rule'][:40] + '...' if len(rule['rule']) > 40 else rule['rule']
                        print(f"   {i}. {rule_preview} ({rule['method']})")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 下拉框切换功能测试完成！")
    
    print("\n📋 新增功能总结:")
    print("✅ 状态检测 - 检查下拉框是否已显示")
    print("✅ 切换逻辑 - 根据状态决定显示或隐藏")
    print("✅ 隐藏函数 - 添加专门的隐藏函数")
    print("✅ 事件优化 - 优化点击事件处理逻辑")
    
    print("\n🔄 切换行为:")
    print("1. 第一次点击输入框 → 下拉框显示")
    print("2. 再次点击输入框 → 下拉框隐藏")
    print("3. 第三次点击输入框 → 下拉框显示")
    print("4. 循环往复，正常切换")
    
    print("\n💡 测试步骤:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 点击任意筛选框，观察下拉框显示")
    print("3. 再次点击同一筛选框，观察下拉框隐藏")
    print("4. 重复点击验证切换功能")
    print("5. 测试输入筛选和选择规则功能")
    
    print("\n⚠️ 注意事项:")
    print("- 输入内容时下拉框会保持显示状态")
    print("- 选择规则后下拉框会自动隐藏")
    print("- 点击页面其他地方会隐藏所有下拉框")
    print("- 焦点事件仍然只显示，不会隐藏")
    
    return True

if __name__ == "__main__":
    test_toggle_functionality()

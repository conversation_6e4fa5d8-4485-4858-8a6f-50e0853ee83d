#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试错误信息显示功能
"""
import requests
import json

def test_error_display():
    """测试错误信息显示功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🚨 测试错误信息显示功能")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript错误处理
    print("\n2️⃣ 测试JavaScript错误处理...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查错误处理增强功能
            error_features = [
                ('getErrorSuggestions', '错误建议函数'),
                ('xhr.responseJSON', 'JSON错误解析'),
                ('xhr.responseText', '文本错误解析'),
                ('alert-danger', '错误样式'),
                ('fas fa-exclamation-triangle', '错误图标'),
                ('解决建议', '建议文本'),
                ('invalid url', 'URL错误检测'),
                ('connection', '连接错误检测'),
                ('xpath', 'XPath错误检测'),
                ('ssl', 'SSL错误检测')
            ]
            
            print("   🔧 检查错误处理功能:")
            for feature_code, description in error_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API错误响应
    print("\n3️⃣ 测试API错误响应...")
    
    # 测试无效URL的错误
    test_cases = [
        {
            'name': 'URL格式错误测试',
            'data': {
                'name': '测试报纸',
                'url': 'jjj',  # 无效URL
                'page_link_rule': '//a/@href',
                'page_link_method': 'xpath'
            },
            'expected_error': 'Invalid URL'
        },
        {
            'name': '空URL测试',
            'data': {
                'name': '测试报纸',
                'url': '',
                'page_link_rule': '//a/@href',
                'page_link_method': 'xpath'
            },
            'expected_error': 'URL'
        },
        {
            'name': '无效规则测试',
            'data': {
                'name': '测试报纸',
                'url': 'https://www.example.com',
                'page_link_rule': '//[invalid xpath',
                'page_link_method': 'xpath'
            },
            'expected_error': 'xpath'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   🧪 {test_case['name']}:")
        try:
            response = requests.post(
                f"{base_url}/api/test/page_links/0",
                json=test_case['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('success') and result.get('message'):
                    print(f"   ✅ 返回预期错误: {result['message'][:100]}...")
                    
                    # 检查错误信息是否包含预期关键词
                    if test_case['expected_error'].lower() in result['message'].lower():
                        print(f"   ✅ 错误信息包含预期关键词: {test_case['expected_error']}")
                    else:
                        print(f"   ⚠️ 错误信息不包含预期关键词: {test_case['expected_error']}")
                else:
                    print(f"   ⚠️ 未返回预期错误，响应: {result}")
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 测试请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 错误信息显示功能测试完成！")
    
    print("\n📋 错误处理增强特性:")
    print("✅ 详细错误信息 - 显示完整的API错误消息")
    print("✅ 错误解析 - 支持JSON和文本格式的错误响应")
    print("✅ 视觉增强 - 使用图标、颜色和样式突出错误")
    print("✅ 智能建议 - 根据错误类型提供解决方案")
    print("✅ 分类处理 - 针对不同错误类型提供专门建议")
    
    print("\n🎯 支持的错误类型:")
    print("- URL格式错误 - 提示添加http://或https://前缀")
    print("- 网络连接错误 - 建议检查网络和目标网站")
    print("- 404/403错误 - 提示检查URL和访问权限")
    print("- XPath规则错误 - 建议使用开发者工具验证")
    print("- 正则表达式错误 - 建议使用在线工具测试")
    print("- SSL证书错误 - 提示证书相关解决方案")
    
    print("\n🎨 错误显示特色:")
    print("- 🚨 醒目的错误图标和红色警告框")
    print("- 📝 清晰的错误信息代码块显示")
    print("- 💡 智能的解决建议列表")
    print("- 🎯 针对性的错误类型检测")
    
    print("\n🚀 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 在URL框中输入无效URL（如 'jjj'）")
    print("3. 填写版面链接规则（如 '//a/@href'）")
    print("4. 点击测试按钮观察错误显示")
    print("5. 查看错误信息和解决建议")
    print("6. 尝试其他类型的错误（空URL、无效规则等）")
    
    print("\n💡 错误信息示例:")
    print("❌ 获取页面内容失败: Invalid URL 'jjj': No scheme supplied. Perhaps you meant https://jjj?")
    print("💡 解决建议:")
    print("   • 请检查URL格式，确保包含 http:// 或 https:// 前缀")
    print("   • 示例：https://www.example.com 或 http://www.example.com")
    
    return True

if __name__ == "__main__":
    test_error_display()

// 添加报纸页面JavaScript

let isEditing = false;
let newspaperId = null;

$(document).ready(function() {
    // 检查是否为编辑模式
    const pathParts = window.location.pathname.split('/');
    if (pathParts[1] === 'edit_newspaper' && pathParts[2]) {
        isEditing = true;
        newspaperId = parseInt(pathParts[2]);
        loadNewspaperData();
    }

    // 加载已有的提取规则
    loadExtractionRules();

    // 初始化下拉框组件
    initDropdownComponents();

    initEventHandlers();
});

// 初始化事件处理器
function initEventHandlers() {
    // 表单提交
    $('#newspaper-form').on('submit', function(e) {
        e.preventDefault();
        saveNewspaper();
    });
    
    // URL输入框失焦时验证
    $('#url').on('blur', function() {
        validateUrl($(this).val());
    });
}

// 加载报纸数据（编辑模式）
function loadNewspaperData() {
    if (!newspaperId) return;
    
    $.get(`/api/newspapers/${newspaperId}`)
        .done(function(response) {
            if (response.success) {
                populateForm(response.data);
                // 更新页面标题
                $('h1').html('<i class="fas fa-edit me-2"></i>编辑报纸');
                $('.breadcrumb-item.active').text('编辑报纸');
                document.title = '编辑报纸 - 报纸采集平台';
            } else {
                showMessage(response.message, 'danger');
            }
        })
        .fail(function() {
            showMessage('加载报纸数据失败', 'danger');
        });
}

// 填充表单数据
function populateForm(data) {
    $('#name').val(data.name);
    $('#url').val(data.url);
    $('#province').val(data.province);
    $('#city').val(data.city);
    $('#url_whitelist').val(data.url_whitelist);
    $('#url_blacklist').val(data.url_blacklist);
    
    $('#page_link_rule').val(data.page_link_rule);
    $('#page_link_method').val(data.page_link_method);
    $('#image_link_rule').val(data.image_link_rule);
    $('#image_link_method').val(data.image_link_method);
    $('#pdf_link_rule').val(data.pdf_link_rule);
    $('#pdf_link_method').val(data.pdf_link_method);
    $('#news_link_rule').val(data.news_link_rule);
    $('#news_link_method').val(data.news_link_method);
}

// 验证URL
function validateUrl(url) {
    if (!url) return;
    
    // 替换日期占位符进行验证
    const testUrl = url.replace(/YYYY/g, '2024')
                      .replace(/MM/g, '01')
                      .replace(/DD/g, '01');
    
    try {
        new URL(testUrl);
        $('#url').removeClass('is-invalid').addClass('is-valid');
    } catch (e) {
        $('#url').removeClass('is-valid').addClass('is-invalid');
    }
}

// 保存报纸
function saveNewspaper() {
    const formData = {
        name: $('#name').val(),
        url: $('#url').val(),
        province: $('#province').val(),
        city: $('#city').val(),
        url_whitelist: $('#url_whitelist').val(),
        url_blacklist: $('#url_blacklist').val(),
        page_link_rule: $('#page_link_rule').val(),
        page_link_method: $('#page_link_method').val(),
        image_link_rule: $('#image_link_rule').val(),
        image_link_method: $('#image_link_method').val(),
        pdf_link_rule: $('#pdf_link_rule').val(),
        pdf_link_method: $('#pdf_link_method').val(),
        news_link_rule: $('#news_link_rule').val(),
        news_link_method: $('#news_link_method').val()
    };
    
    // 验证必填字段
    if (!formData.name || !formData.url) {
        showMessage('请填写报纸名称和URL', 'warning');
        return;
    }
    
    const url = isEditing ? `/api/newspapers/${newspaperId}` : '/api/newspapers';
    const method = isEditing ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                setTimeout(() => {
                    window.location.href = '/newspapers';
                }, 1500);
            } else {
                showMessage(response.message, 'danger');
            }
        },
        error: function() {
            showMessage('保存失败', 'danger');
        }
    });
}

// 测试版面链接提取
function testPageLinks() {
    const name = $('#name').val();
    const url = $('#url').val();
    const rule = $('#page_link_rule').val();
    const method = $('#page_link_method').val();
    
    if (!url || !rule) {
        showMessage('请先填写报纸URL和版面链接提取规则', 'warning');
        return;
    }
    
    // 创建临时报纸对象进行测试
    const testData = {
        name: name || '测试报纸',
        url: url,
        page_link_rule: rule,
        page_link_method: method
    };
    
    showTestLoading('正在测试版面链接提取...');
    
    $.ajax({
        url: '/api/test/page_links/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            if (response.success) {
                showTestResult('版面链接提取测试', response.data, 'success');
            } else {
                showTestResult('版面链接提取测试', {error: response.message}, 'error');
            }
        },
        error: function() {
            showTestResult('版面链接提取测试', {error: '测试请求失败'}, 'error');
        }
    });
}

// 测试双链接提取（版面链接 + 图片/PDF链接）
function testTwoLinks() {
    const name = $('#name').val();
    const url = $('#url').val();
    const pageRule = $('#page_link_rule').val();
    const pageMethod = $('#page_link_method').val();

    if (!url || !pageRule) {
        showMessage('请先填写报纸URL和版面链接提取规则', 'warning');
        return;
    }

    // 创建临时报纸对象进行测试
    const testData = {
        name: name || '测试报纸',
        url: url,
        page_link_rule: pageRule,
        page_link_method: pageMethod
    };

    showTestLoading('正在测试双链接提取...');

    $.ajax({
        url: '/api/test/two_links/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            if (response.success) {
                showTestResult('双链接提取测试', response.data, 'success');
            } else {
                showTestResult('双链接提取测试', {error: response.message}, 'error');
            }
        },
        error: function() {
            showTestResult('双链接提取测试', {error: '测试请求失败'}, 'error');
        }
    });
}

// 测试三链接提取（版面链接 + 图片链接 + PDF链接）
function testThreeLinks() {
    const name = $('#name').val();
    const url = $('#url').val();
    const pageRule = $('#page_link_rule').val();
    const pageMethod = $('#page_link_method').val();

    if (!url || !pageRule) {
        showMessage('请先填写报纸URL和版面链接提取规则', 'warning');
        return;
    }

    // 创建临时报纸对象进行测试
    const testData = {
        name: name || '测试报纸',
        url: url,
        page_link_rule: pageRule,
        page_link_method: pageMethod
    };

    showTestLoading('正在测试三链接提取...');

    $.ajax({
        url: '/api/test/three_links/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            if (response.success) {
                showTestResult('三链接提取测试', response.data, 'success');
            } else {
                showTestResult('三链接提取测试', {error: response.message}, 'error');
            }
        },
        error: function() {
            showTestResult('三链接提取测试', {error: '测试请求失败'}, 'error');
        }
    });
}

// 测试版面内容提取
function testPageContent() {
    const url = $('#url').val();
    const pageRule = $('#page_link_rule').val();
    const pageMethod = $('#page_link_method').val();
    
    if (!url || !pageRule) {
        showMessage('请先填写报纸URL和版面链接提取规则', 'warning');
        return;
    }
    
    // 首先获取版面链接
    const testData = {
        name: '测试报纸',
        url: url,
        page_link_rule: pageRule,
        page_link_method: pageMethod
    };
    
    showTestLoading('正在获取版面链接...');
    
    $.ajax({
        url: '/api/test/page_links/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            if (response.success && response.data.page_links && response.data.page_links.length > 0) {
                // 使用第一个版面链接测试内容提取
                const pageUrl = response.data.page_links[0];
                testPageContentWithUrl(pageUrl);
            } else {
                showTestResult('版面内容提取测试', {error: '未能获取到版面链接'}, 'error');
            }
        },
        error: function() {
            showTestResult('版面内容提取测试', {error: '获取版面链接失败'}, 'error');
        }
    });
}

// 使用指定URL测试版面内容提取
function testPageContentWithUrl(pageUrl) {
    const testData = {
        name: '测试报纸',
        url: $('#url').val(),
        image_link_rule: $('#image_link_rule').val(),
        image_link_method: $('#image_link_method').val(),
        pdf_link_rule: $('#pdf_link_rule').val(),
        pdf_link_method: $('#pdf_link_method').val(),
        news_link_rule: $('#news_link_rule').val(),
        news_link_method: $('#news_link_method').val()
    };
    
    showTestLoading('正在测试版面内容提取...');
    
    $.ajax({
        url: '/api/test/page_content/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({...testData, page_url: pageUrl}),
        success: function(response) {
            if (response.success) {
                showTestResult('版面内容提取测试', response.data, 'success');
            } else {
                showTestResult('版面内容提取测试', {error: response.message}, 'error');
            }
        },
        error: function() {
            showTestResult('版面内容提取测试', {error: '测试请求失败'}, 'error');
        }
    });
}

// 测试完整采集流程
function testFullCollection() {
    const formData = {
        name: $('#name').val() || '测试报纸',
        url: $('#url').val(),
        page_link_rule: $('#page_link_rule').val(),
        page_link_method: $('#page_link_method').val(),
        image_link_rule: $('#image_link_rule').val(),
        image_link_method: $('#image_link_method').val(),
        pdf_link_rule: $('#pdf_link_rule').val(),
        pdf_link_method: $('#pdf_link_method').val(),
        news_link_rule: $('#news_link_rule').val(),
        news_link_method: $('#news_link_method').val()
    };
    
    if (!formData.url || !formData.page_link_rule) {
        showMessage('请先填写报纸URL和版面链接提取规则', 'warning');
        return;
    }
    
    showTestLoading('正在测试完整采集流程，请稍候...');
    
    $.ajax({
        url: '/api/test/full_collection/0',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showTestResult('完整采集流程测试', response.data, 'success');
            } else {
                showTestResult('完整采集流程测试', {error: response.message}, 'error');
            }
        },
        error: function() {
            showTestResult('完整采集流程测试', {error: '测试请求失败'}, 'error');
        }
    });
}

// 显示测试加载状态
function showTestLoading(message) {
    const html = `
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">测试中...</span>
            </div>
            <p class="text-muted">${message}</p>
        </div>
    `;
    $('#test-results').html(html);
}

// 显示测试结果
function showTestResult(title, data, type) {
    let html = `<div class="test-result ${type}">`;
    html += `<h6 class="fw-bold mb-3">${title}</h6>`;
    
    if (type === 'success') {
        if (data.media_links !== undefined) {
            // 双链接测试结果
            html += `
                <p><strong>测试URL:</strong> ${data.url}</p>
                <p><strong>版面链接:</strong> ${data.page_count} 个</p>
                <p><strong>媒体链接:</strong> ${data.media_count} 个（图片:${data.image_count}, PDF:${data.pdf_count}）</p>
                <p><strong>总链接数:</strong> ${data.total_count} 个</p>
                <p><strong>耗时:</strong> ${data.duration} 秒</p>
            `;

            if (data.page_links && data.page_links.length > 0) {
                html += `<div class="mt-3"><strong>版面链接（前10个）:</strong><pre class="mt-2">${data.page_links.join('\n')}</pre></div>`;
            }
            if (data.media_links && data.media_links.length > 0) {
                html += `<div class="mt-2"><strong>媒体链接（前10个）:</strong><pre class="mt-2">${data.media_links.join('\n')}</pre></div>`;
            }
        } else if (data.pdf_links !== undefined && data.image_links !== undefined && data.page_links !== undefined && data.media_links === undefined) {
            // 三链接测试结果
            html += `
                <p><strong>测试URL:</strong> ${data.url}</p>
                <p><strong>版面链接:</strong> ${data.page_count} 个</p>
                <p><strong>图片链接:</strong> ${data.image_count} 个</p>
                <p><strong>PDF链接:</strong> ${data.pdf_count} 个</p>
                <p><strong>总链接数:</strong> ${data.total_count} 个</p>
                <p><strong>耗时:</strong> ${data.duration} 秒</p>
            `;

            if (data.page_links && data.page_links.length > 0) {
                html += `<div class="mt-3"><strong>版面链接（前10个）:</strong><pre class="mt-2">${data.page_links.join('\n')}</pre></div>`;
            }
            if (data.image_links && data.image_links.length > 0) {
                html += `<div class="mt-2"><strong>图片链接（前10个）:</strong><pre class="mt-2">${data.image_links.join('\n')}</pre></div>`;
            }
            if (data.pdf_links && data.pdf_links.length > 0) {
                html += `<div class="mt-2"><strong>PDF链接（前10个）:</strong><pre class="mt-2">${data.pdf_links.join('\n')}</pre></div>`;
            }
        } else if (data.page_links && data.total_count !== undefined && data.media_links === undefined) {
            // 单一版面链接测试结果
            html += `
                <p><strong>测试URL:</strong> ${data.url}</p>
                <p><strong>提取到版面链接:</strong> ${data.total_count} 个</p>
                <p><strong>耗时:</strong> ${data.duration} 秒</p>
                <div class="mt-3">
                    <strong>版面链接列表（前10个）:</strong>
                    <pre class="mt-2">${data.page_links.join('\n')}</pre>
                </div>
            `;
        } else if (data.image_links !== undefined && data.page_url !== undefined) {
            // 版面内容测试结果
            html += `
                <p><strong>测试页面:</strong> ${data.page_url}</p>
                <p><strong>图片链接:</strong> ${data.image_links.length} 个</p>
                <p><strong>PDF链接:</strong> ${data.pdf_links.length} 个</p>
                <p><strong>新闻链接:</strong> ${data.news_links.length} 个</p>
                <p><strong>耗时:</strong> ${data.duration} 秒</p>
            `;
            
            if (data.image_links.length > 0) {
                html += `<div class="mt-2"><strong>图片链接:</strong><pre>${data.image_links.slice(0, 5).join('\n')}</pre></div>`;
            }
            if (data.pdf_links.length > 0) {
                html += `<div class="mt-2"><strong>PDF链接:</strong><pre>${data.pdf_links.slice(0, 5).join('\n')}</pre></div>`;
            }
            if (data.news_links.length > 0) {
                html += `<div class="mt-2"><strong>新闻链接:</strong><pre>${data.news_links.slice(0, 5).join('\n')}</pre></div>`;
            }
        } else if (data.total_pages !== undefined) {
            // 完整采集测试结果
            html += `
                <p><strong>总版面数:</strong> ${data.total_pages}</p>
                <p><strong>测试版面数:</strong> ${data.tested_pages}</p>
                <p><strong>耗时:</strong> ${data.duration} 秒</p>
                <div class="mt-3">
                    <strong>测试结果摘要:</strong>
                    <ul class="mt-2">
            `;
            
            data.results.forEach((result, index) => {
                if (result.error) {
                    html += `<li>版面 ${index + 1}: <span class="text-danger">失败 - ${result.error}</span></li>`;
                } else {
                    const imageCount = result.image_links ? result.image_links.length : 0;
                    const pdfCount = result.pdf_links ? result.pdf_links.length : 0;
                    const newsCount = result.news_links ? result.news_links.length : 0;
                    html += `<li>版面 ${index + 1}: <span class="text-success">成功</span> - 图片:${imageCount}, PDF:${pdfCount}, 新闻:${newsCount}</li>`;
                }
            });
            
            html += '</ul></div>';
        }
    } else {
        // 错误结果
        html += `<div class="alert alert-danger">${data.error}</div>`;
    }
    
    html += '</div>';
    $('#test-results').html(html);
}

// 加载已有的提取规则
function loadExtractionRules() {
    $.ajax({
        url: '/api/extraction_rules',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                populateRuleSelects(response.data);
            } else {
                console.error('加载提取规则失败:', response.message);
            }
        },
        error: function() {
            console.error('加载提取规则请求失败');
        }
    });
}

// 填充规则下拉框
function populateRuleSelects(rules) {
    // 保存规则数据到全局变量
    window.extractionRulesData = rules;

    // 初始化所有下拉框
    populatePageLinkRules();
    populateImageLinkRules();
    populatePdfLinkRules();
    populateNewsLinkRules();
}

// 填充版面链接规则下拉框
function populatePageLinkRules(filter = '') {
    const dropdown = $('#page_link_rule_dropdown');
    dropdown.empty();

    if (!window.extractionRulesData || !window.extractionRulesData.page_link_rules) {
        dropdown.append('<li><a class="dropdown-item text-muted">暂无规则</a></li>');
        return;
    }

    const rules = window.extractionRulesData.page_link_rules;
    const filteredRules = filter ? rules.filter(rule =>
        rule.rule.toLowerCase().includes(filter.toLowerCase()) ||
        rule.method.toLowerCase().includes(filter.toLowerCase()) ||
        rule.newspaper_name.toLowerCase().includes(filter.toLowerCase())
    ) : rules;

    if (filteredRules.length === 0) {
        dropdown.append('<li><a class="dropdown-item text-muted">未找到匹配的规则</a></li>');
        return;
    }

    filteredRules.forEach(function(rule, index) {
        const originalIndex = rules.indexOf(rule);
        const ruleText = rule.rule.length > 60 ? rule.rule.substring(0, 60) + '...' : rule.rule;
        const itemHtml = `
            <li>
                <a class="dropdown-item" href="#" onclick="selectPageLinkRuleByIndex(${originalIndex}); return false;">
                    <div class="fw-bold">${ruleText}</div>
                    <small class="text-muted">${rule.method} - ${rule.newspaper_name}</small>
                </a>
            </li>
        `;
        dropdown.append(itemHtml);
    });
}

// 填充图片链接规则下拉框
function populateImageLinkRules(filter = '') {
    const dropdown = $('#image_link_rule_dropdown');
    dropdown.empty();

    if (!window.extractionRulesData || !window.extractionRulesData.image_link_rules) {
        dropdown.append('<li><a class="dropdown-item text-muted">暂无规则</a></li>');
        return;
    }

    const rules = window.extractionRulesData.image_link_rules;
    const filteredRules = filter ? rules.filter(rule =>
        rule.rule.toLowerCase().includes(filter.toLowerCase()) ||
        rule.method.toLowerCase().includes(filter.toLowerCase()) ||
        rule.newspaper_name.toLowerCase().includes(filter.toLowerCase())
    ) : rules;

    if (filteredRules.length === 0) {
        dropdown.append('<li><a class="dropdown-item text-muted">未找到匹配的规则</a></li>');
        return;
    }

    filteredRules.forEach(function(rule, index) {
        const originalIndex = rules.indexOf(rule);
        const ruleText = rule.rule.length > 60 ? rule.rule.substring(0, 60) + '...' : rule.rule;
        const itemHtml = `
            <li>
                <a class="dropdown-item" href="#" onclick="selectImageLinkRuleByIndex(${originalIndex}); return false;">
                    <div class="fw-bold">${ruleText}</div>
                    <small class="text-muted">${rule.method} - ${rule.newspaper_name}</small>
                </a>
            </li>
        `;
        dropdown.append(itemHtml);
    });
}

// 填充PDF链接规则下拉框
function populatePdfLinkRules(filter = '') {
    const dropdown = $('#pdf_link_rule_dropdown');
    dropdown.empty();

    if (!window.extractionRulesData || !window.extractionRulesData.pdf_link_rules) {
        dropdown.append('<li><a class="dropdown-item text-muted">暂无规则</a></li>');
        return;
    }

    const rules = window.extractionRulesData.pdf_link_rules;
    const filteredRules = filter ? rules.filter(rule =>
        rule.rule.toLowerCase().includes(filter.toLowerCase()) ||
        rule.method.toLowerCase().includes(filter.toLowerCase()) ||
        rule.newspaper_name.toLowerCase().includes(filter.toLowerCase())
    ) : rules;

    if (filteredRules.length === 0) {
        dropdown.append('<li><a class="dropdown-item text-muted">未找到匹配的规则</a></li>');
        return;
    }

    filteredRules.forEach(function(rule, index) {
        const originalIndex = rules.indexOf(rule);
        const ruleText = rule.rule.length > 60 ? rule.rule.substring(0, 60) + '...' : rule.rule;
        const itemHtml = `
            <li>
                <a class="dropdown-item" href="#" onclick="selectPdfLinkRuleByIndex(${originalIndex}); return false;">
                    <div class="fw-bold">${ruleText}</div>
                    <small class="text-muted">${rule.method} - ${rule.newspaper_name}</small>
                </a>
            </li>
        `;
        dropdown.append(itemHtml);
    });
}

// 填充新闻链接规则下拉框
function populateNewsLinkRules(filter = '') {
    const dropdown = $('#news_link_rule_dropdown');
    dropdown.empty();

    if (!window.extractionRulesData || !window.extractionRulesData.news_link_rules) {
        dropdown.append('<li><a class="dropdown-item text-muted">暂无规则</a></li>');
        return;
    }

    const rules = window.extractionRulesData.news_link_rules;
    const filteredRules = filter ? rules.filter(rule =>
        rule.rule.toLowerCase().includes(filter.toLowerCase()) ||
        rule.method.toLowerCase().includes(filter.toLowerCase()) ||
        rule.newspaper_name.toLowerCase().includes(filter.toLowerCase())
    ) : rules;

    if (filteredRules.length === 0) {
        dropdown.append('<li><a class="dropdown-item text-muted">未找到匹配的规则</a></li>');
        return;
    }

    filteredRules.forEach(function(rule, index) {
        const originalIndex = rules.indexOf(rule);
        const ruleText = rule.rule.length > 60 ? rule.rule.substring(0, 60) + '...' : rule.rule;
        const itemHtml = `
            <li>
                <a class="dropdown-item" href="#" onclick="selectNewsLinkRuleByIndex(${originalIndex}); return false;">
                    <div class="fw-bold">${ruleText}</div>
                    <small class="text-muted">${rule.method} - ${rule.newspaper_name}</small>
                </a>
            </li>
        `;
        dropdown.append(itemHtml);
    });
}

// 筛选函数（优化版）
function filterPageLinkRules() {
    const filter = $('#page_link_rule_filter').val();
    populatePageLinkRules(filter);

    // 确保下拉框保持显示状态
    const element = $('#page_link_rule_filter')[0];
    const dropdown = bootstrap.Dropdown.getInstance(element);
    if (dropdown && !$('#page_link_rule_dropdown').hasClass('show')) {
        setTimeout(() => dropdown.show(), 10);
    }
}

function filterImageLinkRules() {
    const filter = $('#image_link_rule_filter').val();
    populateImageLinkRules(filter);

    const element = $('#image_link_rule_filter')[0];
    const dropdown = bootstrap.Dropdown.getInstance(element);
    if (dropdown && !$('#image_link_rule_dropdown').hasClass('show')) {
        setTimeout(() => dropdown.show(), 10);
    }
}

function filterPdfLinkRules() {
    const filter = $('#pdf_link_rule_filter').val();
    populatePdfLinkRules(filter);

    const element = $('#pdf_link_rule_filter')[0];
    const dropdown = bootstrap.Dropdown.getInstance(element);
    if (dropdown && !$('#pdf_link_rule_dropdown').hasClass('show')) {
        setTimeout(() => dropdown.show(), 10);
    }
}

function filterNewsLinkRules() {
    const filter = $('#news_link_rule_filter').val();
    populateNewsLinkRules(filter);

    const element = $('#news_link_rule_filter')[0];
    const dropdown = bootstrap.Dropdown.getInstance(element);
    if (dropdown && !$('#news_link_rule_dropdown').hasClass('show')) {
        setTimeout(() => dropdown.show(), 10);
    }
}

// 显示下拉框函数（优化版）
function showPageLinkRules() {
    setTimeout(() => {
        const element = $('#page_link_rule_filter')[0];
        let dropdown = bootstrap.Dropdown.getInstance(element);
        if (!dropdown) {
            dropdown = new bootstrap.Dropdown(element);
        }
        dropdown.show();
    }, 10);
}

function showImageLinkRules() {
    setTimeout(() => {
        const element = $('#image_link_rule_filter')[0];
        let dropdown = bootstrap.Dropdown.getInstance(element);
        if (!dropdown) {
            dropdown = new bootstrap.Dropdown(element);
        }
        dropdown.show();
    }, 10);
}

function showPdfLinkRules() {
    setTimeout(() => {
        const element = $('#pdf_link_rule_filter')[0];
        let dropdown = bootstrap.Dropdown.getInstance(element);
        if (!dropdown) {
            dropdown = new bootstrap.Dropdown(element);
        }
        dropdown.show();
    }, 10);
}

function showNewsLinkRules() {
    setTimeout(() => {
        const element = $('#news_link_rule_filter')[0];
        let dropdown = bootstrap.Dropdown.getInstance(element);
        if (!dropdown) {
            dropdown = new bootstrap.Dropdown(element);
        }
        dropdown.show();
    }, 10);
}

// 优化的事件处理函数
function handlePageLinkRuleClick() {
    // 防止事件冲突，延迟执行
    setTimeout(() => {
        populatePageLinkRules($('#page_link_rule_filter').val());
        showPageLinkRules();
    }, 50);
}

function handlePageLinkRuleFocus() {
    // 焦点事件处理
    setTimeout(() => {
        populatePageLinkRules($('#page_link_rule_filter').val());
        showPageLinkRules();
    }, 100);
}

function handleImageLinkRuleClick() {
    setTimeout(() => {
        populateImageLinkRules($('#image_link_rule_filter').val());
        showImageLinkRules();
    }, 50);
}

function handleImageLinkRuleFocus() {
    setTimeout(() => {
        populateImageLinkRules($('#image_link_rule_filter').val());
        showImageLinkRules();
    }, 100);
}

function handlePdfLinkRuleClick() {
    setTimeout(() => {
        populatePdfLinkRules($('#pdf_link_rule_filter').val());
        showPdfLinkRules();
    }, 50);
}

function handlePdfLinkRuleFocus() {
    setTimeout(() => {
        populatePdfLinkRules($('#pdf_link_rule_filter').val());
        showPdfLinkRules();
    }, 100);
}

function handleNewsLinkRuleClick() {
    setTimeout(() => {
        populateNewsLinkRules($('#news_link_rule_filter').val());
        showNewsLinkRules();
    }, 50);
}

function handleNewsLinkRuleFocus() {
    setTimeout(() => {
        populateNewsLinkRules($('#news_link_rule_filter').val());
        showNewsLinkRules();
    }, 100);
}

// 选择规则函数（通过索引）
function selectPageLinkRuleByIndex(index) {
    if (window.extractionRulesData && window.extractionRulesData.page_link_rules[index]) {
        const ruleData = window.extractionRulesData.page_link_rules[index];
        $('#page_link_rule').val(ruleData.rule);
        $('#page_link_method').val(ruleData.method);
        $('#page_link_rule_filter').val(''); // 清空筛选框

        // 隐藏下拉框
        const dropdown = bootstrap.Dropdown.getInstance($('#page_link_rule_filter')[0]);
        if (dropdown) dropdown.hide();
    }
}

function selectImageLinkRuleByIndex(index) {
    if (window.extractionRulesData && window.extractionRulesData.image_link_rules[index]) {
        const ruleData = window.extractionRulesData.image_link_rules[index];
        $('#image_link_rule').val(ruleData.rule);
        $('#image_link_method').val(ruleData.method);
        $('#image_link_rule_filter').val(''); // 清空筛选框

        // 隐藏下拉框
        const dropdown = bootstrap.Dropdown.getInstance($('#image_link_rule_filter')[0]);
        if (dropdown) dropdown.hide();
    }
}

function selectPdfLinkRuleByIndex(index) {
    if (window.extractionRulesData && window.extractionRulesData.pdf_link_rules[index]) {
        const ruleData = window.extractionRulesData.pdf_link_rules[index];
        $('#pdf_link_rule').val(ruleData.rule);
        $('#pdf_link_method').val(ruleData.method);
        $('#pdf_link_rule_filter').val(''); // 清空筛选框

        // 隐藏下拉框
        const dropdown = bootstrap.Dropdown.getInstance($('#pdf_link_rule_filter')[0]);
        if (dropdown) dropdown.hide();
    }
}

function selectNewsLinkRuleByIndex(index) {
    if (window.extractionRulesData && window.extractionRulesData.news_link_rules[index]) {
        const ruleData = window.extractionRulesData.news_link_rules[index];
        $('#news_link_rule').val(ruleData.rule);
        $('#news_link_method').val(ruleData.method);
        $('#news_link_rule_filter').val(''); // 清空筛选框

        // 隐藏下拉框
        const dropdown = bootstrap.Dropdown.getInstance($('#news_link_rule_filter')[0]);
        if (dropdown) dropdown.hide();
    }
}

// 初始化下拉框组件
function initDropdownComponents() {
    // 为所有筛选框添加事件监听器，防止默认的Bootstrap行为
    const filterInputs = [
        '#page_link_rule_filter',
        '#image_link_rule_filter',
        '#pdf_link_rule_filter',
        '#news_link_rule_filter'
    ];

    filterInputs.forEach(selector => {
        const element = $(selector)[0];
        if (element) {
            // 阻止默认的Bootstrap下拉框行为
            element.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // 添加键盘事件支持
            element.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    // 可以在这里添加键盘导航逻辑
                }
            });
        }
    });

    // 点击页面其他地方时隐藏所有下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').removeClass('show');
        }
    });
}

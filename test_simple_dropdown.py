#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试最简化的下拉框实现
"""
import requests
import json

def test_simple_dropdown():
    """测试最简化的下拉框实现"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔧 测试最简化的下拉框实现")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查HTML结构
            content = response.text
            
            # 检查是否移除了onclick事件
            if 'onclick="togglePageLinkRules()"' in content:
                print("   ⚠️ 仍然包含onclick事件")
            else:
                print("   ✅ 已移除onclick事件")
            
            # 检查是否只保留了oninput事件
            if 'oninput="filterPageLinkRules()"' in content:
                print("   ✅ 保留了oninput事件")
            else:
                print("   ❌ 缺少oninput事件")
                
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript实现
    print("\n2️⃣ 测试JavaScript实现...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查简化实现的关键特征
            simple_features = [
                ('togglePageLinkRulesSimple', '简化切换函数'),
                ('dropdown.is(\':visible\')', 'jQuery可见性检测'),
                ('console.log', '调试日志'),
                ('e.stopPropagation()', '事件冒泡阻止'),
                ('hideAllDropdowns', '隐藏所有下拉框函数'),
                ('.on(\'click\'', 'jQuery事件绑定')
            ]
            
            print("   🔧 检查简化实现特征:")
            for feature_code, description in simple_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
            
            # 检查是否完全移除了Bootstrap
            if 'bootstrap' in js_content.lower():
                print("   ⚠️ 仍然包含Bootstrap代码")
            else:
                print("   ✅ 完全移除了Bootstrap代码")
                
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 最简化下拉框测试完成！")
    
    print("\n📋 简化方案特点:")
    print("✅ 移除HTML onclick事件 - 避免事件冲突")
    print("✅ 使用jQuery事件绑定 - 更好的控制")
    print("✅ 添加调试日志 - 便于问题排查")
    print("✅ 使用is(':visible') - 更可靠的状态检测")
    print("✅ 事件冒泡阻止 - 避免意外触发")
    print("✅ 完全移除Bootstrap - 消除所有冲突源")
    
    print("\n🔄 预期行为:")
    print("1. 点击输入框 → 控制台显示日志 → 下拉框切换")
    print("2. 可以通过浏览器开发者工具查看控制台日志")
    print("3. 下拉框状态应该正确切换")
    
    print("\n🛠️ 调试建议:")
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 切换到Console标签")
    print("3. 点击筛选框观察控制台输出")
    print("4. 查看是否有错误信息")
    
    print("\n🚀 测试步骤:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 打开开发者工具查看控制台")
    print("3. 点击版面链接筛选框")
    print("4. 观察控制台日志和下拉框行为")
    print("5. 如果仍有问题，查看控制台错误信息")
    
    return True

if __name__ == "__main__":
    test_simple_dropdown()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试不同排序参数
"""
import requests

def test_sorting_params():
    """测试不同排序参数"""
    print('测试不同排序参数:')

    # 测试默认排序
    print('1. 默认排序:')
    r1 = requests.get('http://127.0.0.1:5009/api/newspapers')
    if r1.status_code == 200:
        data1 = r1.json()
        if data1['success']:
            newspapers1 = data1['data']
            for i, n in enumerate(newspapers1[:3], 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'   {i}. {name} (ID: {newspaper_id}) - {created_at}')

    # 测试明确指定倒序排序
    print('\n2. 明确指定创建时间倒序:')
    r2 = requests.get('http://127.0.0.1:5009/api/newspapers?sort_by=created_at&sort_order=desc')
    if r2.status_code == 200:
        data2 = r2.json()
        if data2['success']:
            newspapers2 = data2['data']
            for i, n in enumerate(newspapers2[:3], 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'   {i}. {name} (ID: {newspaper_id}) - {created_at}')
                
    # 测试正序排序
    print('\n3. 创建时间正序:')
    r3 = requests.get('http://127.0.0.1:5009/api/newspapers?sort_by=created_at&sort_order=asc')
    if r3.status_code == 200:
        data3 = r3.json()
        if data3['success']:
            newspapers3 = data3['data']
            for i, n in enumerate(newspapers3[:3], 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'   {i}. {name} (ID: {newspaper_id}) - {created_at}')

if __name__ == "__main__":
    test_sorting_params()

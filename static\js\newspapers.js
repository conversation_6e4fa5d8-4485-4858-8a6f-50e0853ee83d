// 报纸列表页面JavaScript

let currentPage = 1;
let currentFilters = {};
let selectedIds = [];

$(document).ready(function() {
    loadNewspapers();
    initEventHandlers();
});

// 初始化事件处理器
function initEventHandlers() {
    // 筛选表单提交
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadNewspapers();
    });
    
    // 全选/取消全选
    $('#select-all').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.row-checkbox').prop('checked', isChecked);
        updateSelectedCount();
    });
    
    // 行选择
    $(document).on('change', '.row-checkbox', function() {
        updateSelectedCount();
        updateSelectAllState();
    });
}

// 加载报纸列表
function loadNewspapers() {
    // 获取筛选条件
    currentFilters = {
        page: currentPage,
        per_page: 20,
        name: $('#filter-name').val(),
        province: $('#filter-province').val(),
        city: $('#filter-city').val(),
        collection_status: $('#filter-collection-status').val(),
        publish_status: $('#filter-publish-status').val()
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key] === '') {
            delete currentFilters[key];
        }
    });
    
    showLoading('#newspapers-table');
    
    $.get('/api/newspapers', currentFilters)
        .done(function(response) {
            if (response.success) {
                renderNewspapersTable(response.data);
                renderPagination(response.pagination);
            } else {
                showMessage(response.message, 'danger');
            }
        })
        .fail(function() {
            $('#newspapers-table').html('<tr><td colspan="8" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 渲染报纸表格
function renderNewspapersTable(newspapers) {
    const tbody = $('#newspapers-table');
    tbody.empty();
    
    if (newspapers.length === 0) {
        tbody.html('<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>');
        return;
    }
    
    newspapers.forEach(function(newspaper) {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input row-checkbox" value="${newspaper.id}">
                </td>
                <td>
                    <div class="fw-bold">${newspaper.name}</div>
                    <small class="text-muted">${newspaper.url}</small>
                </td>
                <td>
                    <div>${newspaper.province || '-'}</div>
                    <small class="text-muted">${newspaper.city || '-'}</small>
                </td>
                <td>${getStatusBadge(newspaper.collection_status, 'collection')}</td>
                <td>
                    <div>${formatDateTime(newspaper.last_collection_time)}</div>
                </td>
                <td>${formatDuration(newspaper.collection_duration)}</td>
                <td>${getStatusBadge(newspaper.publish_status, 'publish')}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-primary" onclick="editNewspaper(${newspaper.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="collectSingle(${newspaper.id})" title="单份采集">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="testNewspaper(${newspaper.id})" title="测试采集">
                            <i class="fas fa-vial"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="viewResults(${newspaper.id})" title="查看结果">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteNewspaper(${newspaper.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 渲染分页
function renderPagination(pagination) {
    const paginationHtml = generatePagination(pagination, 'goToPage');
    $('#pagination-container').html(paginationHtml);
}

// 跳转到指定页面
function goToPage(page) {
    currentPage = page;
    loadNewspapers();
}

// 更新选中数量
function updateSelectedCount() {
    selectedIds = $('.row-checkbox:checked').map(function() {
        return parseInt($(this).val());
    }).get();
    $('#selected-count').text(selectedIds.length);
}

// 更新全选状态
function updateSelectAllState() {
    const totalCheckboxes = $('.row-checkbox').length;
    const checkedCheckboxes = $('.row-checkbox:checked').length;
    
    if (checkedCheckboxes === 0) {
        $('#select-all').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all').prop('indeterminate', true);
    }
}

// 重置筛选条件
function resetFilters() {
    $('#filter-form')[0].reset();
    currentPage = 1;
    loadNewspapers();
}

// 编辑报纸
function editNewspaper(id) {
    window.location.href = `/edit_newspaper/${id}`;
}

// 单份采集
function collectSingle(id) {
    if (confirm('确定要开始采集这份报纸吗？')) {
        $.post(`/api/collection/single/${id}`)
            .done(function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    loadNewspapers(); // 刷新列表
                } else {
                    showMessage(response.message, 'danger');
                }
            });
    }
}

// 测试报纸采集
function testNewspaper(id) {
    $.post(`/api/test/full_collection/${id}`)
        .done(function(response) {
            if (response.success) {
                showTestResults(response.data);
            } else {
                showMessage(response.message, 'danger');
            }
        });
}

// 显示测试结果
function showTestResults(data) {
    let html = `
        <div class="modal fade" id="testResultModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">测试结果</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <strong>测试摘要：</strong>${data.message}<br>
                            <strong>总版面数：</strong>${data.total_pages}<br>
                            <strong>测试版面数：</strong>${data.tested_pages}<br>
                            <strong>耗时：</strong>${formatDuration(data.duration)}
                        </div>
                        <div class="accordion" id="testAccordion">
    `;
    
    data.results.forEach(function(result, index) {
        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                        版面 ${index + 1}: ${result.page_link}
                    </button>
                </h2>
                <div id="collapse${index}" class="accordion-collapse collapse" data-bs-parent="#testAccordion">
                    <div class="accordion-body">
                        ${result.error ? 
                            `<div class="alert alert-danger">错误: ${result.error}</div>` :
                            `
                            <p><strong>图片链接:</strong> ${result.image_links ? result.image_links.length : 0} 个</p>
                            <p><strong>PDF链接:</strong> ${result.pdf_links ? result.pdf_links.length : 0} 个</p>
                            <p><strong>新闻链接:</strong> ${result.news_links ? result.news_links.length : 0} 个</p>
                            `
                        }
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除已存在的模态框
    $('#testResultModal').remove();
    $('body').append(html);
    $('#testResultModal').modal('show');
}

// 查看采集结果
function viewResults(id) {
    window.location.href = `/collection_results?newspaper_id=${id}`;
}

// 删除报纸
function deleteNewspaper(id) {
    if (confirm('确定要删除这份报纸吗？此操作不可撤销。')) {
        $.ajax({
            url: `/api/newspapers/${id}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    loadNewspapers(); // 刷新列表
                } else {
                    showMessage(response.message, 'danger');
                }
            }
        });
    }
}

// 批量采集
function batchCollect() {
    if (selectedIds.length === 0) {
        showMessage('请先选择要采集的报纸', 'warning');
        return;
    }
    
    if (confirm(`确定要采集选中的 ${selectedIds.length} 份报纸吗？`)) {
        $.ajax({
            url: '/api/collection/batch',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({newspaper_ids: selectedIds}),
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    loadNewspapers(); // 刷新列表
                } else {
                    showMessage(response.message, 'danger');
                }
            }
        });
    }
}

// 批量删除
function batchDelete() {
    if (selectedIds.length === 0) {
        showMessage('请先选择要删除的报纸', 'warning');
        return;
    }
    
    $('#deleteModal').modal('show');
}

// 确认删除
function confirmDelete() {
    const promises = selectedIds.map(id => {
        return $.ajax({
            url: `/api/newspapers/${id}`,
            method: 'DELETE'
        });
    });
    
    Promise.all(promises)
        .then(function() {
            $('#deleteModal').modal('hide');
            showMessage(`成功删除 ${selectedIds.length} 份报纸`, 'success');
            loadNewspapers(); // 刷新列表
        })
        .catch(function() {
            $('#deleteModal').modal('hide');
            showMessage('删除失败', 'danger');
        });
}

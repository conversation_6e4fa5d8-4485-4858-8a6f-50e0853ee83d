{% extends "base.html" %}

{% block title %}报纸管理 - 报纸采集平台{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">报纸管理</li>
    </ol>
</nav>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-list me-2"></i>报纸管理
    </h1>
    <div>
        <a href="{{ url_for('add_newspaper') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加报纸
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 筛选条件 -->
<div class="card mb-4">
    <div class="card-body">
        <form id="filter-form" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">报纸名称</label>
                <input type="text" class="form-control" id="filter-name" placeholder="输入报纸名称">
            </div>
            <div class="col-md-2">
                <label class="form-label">省份</label>
                <select class="form-select" id="filter-province">
                    <option value="">全部省份</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">城市</label>
                <select class="form-select" id="filter-city">
                    <option value="">全部城市</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">采集状态</label>
                <select class="form-select" id="filter-collection-status">
                    <option value="">全部状态</option>
                    <option value="未采集">未采集</option>
                    <option value="采集中">采集中</option>
                    <option value="采集成功">采集成功</option>
                    <option value="采集失败">采集失败</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">发布状态</label>
                <select class="form-select" id="filter-publish-status">
                    <option value="">全部状态</option>
                    <option value="已发布">已发布</option>
                    <option value="未发布">未发布</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 批量操作 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button class="btn btn-success" onclick="batchCollect()">
                    <i class="fas fa-play me-1"></i>批量采集
                </button>
                <button class="btn btn-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
            </div>
            <div>
                <span class="text-muted">已选择 <span id="selected-count">0</span> 项</span>
            </div>
        </div>
    </div>
</div>

<!-- 报纸列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>报纸名称</th>
                        <th>省市</th>
                        <th>采集状态</th>
                        <th>最后采集时间</th>
                        <th>运行时间</th>
                        <th>发布状态</th>
                        <th width="200">操作</th>
                    </tr>
                </thead>
                <tbody id="newspapers-table">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav id="pagination-container" class="mt-3"></nav>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的报纸吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/newspapers.js') }}"></script>
{% endblock %}

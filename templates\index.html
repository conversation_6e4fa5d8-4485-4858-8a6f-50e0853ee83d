{% extends "base.html" %}

{% block title %}首页 - 报纸采集平台{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>数据概览
    </h1>
    <div>
        <a href="{{ url_for('add_newspaper') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加报纸
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="total-newspapers">-</h4>
                        <p class="card-text">总报纸数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-newspaper fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="today-collected">-</h4>
                        <p class="card-text">当天已采集</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="today-not-collected">-</h4>
                        <p class="card-text">当天未采集</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="today-results">-</h4>
                        <p class="card-text">当天采集结果</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 省份统计 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>省份分布
                </h5>
            </div>
            <div class="card-body">
                <div id="province-stats">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>快捷操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_newspaper') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>添加新报纸
                    </a>
                    <a href="{{ url_for('newspapers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>管理报纸列表
                    </a>
                    <button class="btn btn-outline-success" onclick="startBatchCollection()">
                        <i class="fas fa-play me-2"></i>开始批量采集
                    </button>
                    <a href="{{ url_for('collection_results') }}" class="btn btn-outline-info">
                        <i class="fas fa-search me-2"></i>查看采集结果
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量采集模态框 -->
<div class="modal fade" id="batchCollectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量采集</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要开始批量采集所有启用的报纸吗？</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    批量采集可能需要较长时间，请耐心等待。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmBatchCollection()">
                    <i class="fas fa-play me-1"></i>开始采集
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadDashboardData();
});

function loadDashboardData() {
    $.get('/api/statistics/dashboard')
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#total-newspapers').text(data.total_newspapers);
                $('#today-collected').text(data.today_collected);
                $('#today-not-collected').text(data.today_not_collected);
                $('#today-results').text(data.today_results);
                
                // 渲染省份统计
                renderProvinceStats(data.province_stats);
            }
        })
        .fail(function() {
            showMessage('加载统计数据失败', 'danger');
        });
}

function renderProvinceStats(stats) {
    const container = $('#province-stats');
    container.empty();
    
    if (stats.length === 0) {
        container.html('<p class="text-muted text-center">暂无数据</p>');
        return;
    }
    
    stats.forEach(function(item) {
        const html = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${item.province || '未知'}</span>
                <span class="badge bg-primary">${item.count}</span>
            </div>
        `;
        container.append(html);
    });
}

function startBatchCollection() {
    $('#batchCollectionModal').modal('show');
}

function confirmBatchCollection() {
    // 获取所有启用的报纸ID
    $.get('/api/newspapers?per_page=1000')
        .done(function(response) {
            if (response.success) {
                const newspaperIds = response.data
                    .filter(n => n.is_active)
                    .map(n => n.id);
                
                if (newspaperIds.length === 0) {
                    showMessage('没有启用的报纸可以采集', 'warning');
                    return;
                }
                
                // 开始批量采集
                $.ajax({
                    url: '/api/collection/batch',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({newspaper_ids: newspaperIds}),
                    success: function(response) {
                        $('#batchCollectionModal').modal('hide');
                        if (response.success) {
                            showMessage(response.message, 'success');
                            // 刷新统计数据
                            setTimeout(loadDashboardData, 2000);
                        } else {
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        $('#batchCollectionModal').modal('hide');
                        showMessage('批量采集启动失败', 'danger');
                    }
                });
            }
        });
}
</script>
{% endblock %}

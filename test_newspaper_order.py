#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试报纸列表排序
"""
import requests

def test_newspaper_order():
    """测试报纸列表排序"""
    print('测试报纸列表排序:')
    try:
        r = requests.get('http://127.0.0.1:5009/api/newspapers')
        if r.status_code == 200:
            data = r.json()
            if data['success']:
                newspapers = data['data']
                print(f'总共 {len(newspapers)} 个报纸:')
                for i, newspaper in enumerate(newspapers, 1):
                    name = newspaper['name']
                    newspaper_id = newspaper['id']
                    created_at = newspaper['created_at']
                    print(f'{i}. {name} (ID: {newspaper_id}) - 创建时间: {created_at}')
                    
                print('\n当前排序分析:')
                if len(newspapers) >= 2:
                    first = newspapers[0]
                    second = newspapers[1]
                    print(f'第一个: {first["name"]} - {first["created_at"]}')
                    print(f'第二个: {second["name"]} - {second["created_at"]}')
                    
                    # 比较创建时间
                    from datetime import datetime
                    first_time = datetime.fromisoformat(first['created_at'].replace('Z', '+00:00'))
                    second_time = datetime.fromisoformat(second['created_at'].replace('Z', '+00:00'))
                    
                    if first_time > second_time:
                        print('✅ 排序正确：最新的报纸在第一位')
                    else:
                        print('❌ 排序错误：最新的报纸不在第一位')
                else:
                    print('报纸数量不足，无法比较排序')
            else:
                print(f'API错误: {data.get("message")}')
        else:
            print(f'HTTP错误: {r.status_code}')
    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == "__main__":
    test_newspaper_order()

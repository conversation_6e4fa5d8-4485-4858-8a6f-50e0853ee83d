# 报纸采集平台

一个基于Flask的报纸采集平台，支持自定义采集规则，实现对报纸版面链接、版面图片链接、版面PDF链接、版面新闻链接信息的采集。

## 功能特性

### 核心功能
- 🗞️ **报纸管理**: 添加、编辑、删除报纸信息
- 🔧 **采集规则配置**: 支持XPath、正则表达式、BeautifulSoup三种提取方式
- 🚀 **多种采集模式**: 单份采集、批量采集、定时采集
- 🧪 **规则测试**: 版面链接测试、版面内容测试、完整采集流程测试
- 📊 **结果管理**: 采集结果查看、筛选、发布状态管理
- 📈 **统计分析**: 采集统计、省份分布、发布状态统计

### 技术特性
- 📅 **日期占位符**: 支持YYYY、MM、DD日期占位符，自动替换为当前日期
- 🔍 **URL过滤**: 支持白名单和黑名单URL过滤
- ⚡ **频率控制**: 内置请求频率限制，避免对目标网站造成压力
- 🔄 **重试机制**: 自动重试失败的请求
- 📱 **响应式设计**: 支持PC和移动端访问

## 系统架构

```
报纸采集平台/
├── app.py                 # Flask主应用
├── run.py                 # 启动脚本
├── config.py              # 配置文件
├── database.py            # 数据库初始化
├── models.py              # 数据库模型
├── api.py                 # API接口
├── requirements.txt       # 依赖包列表
├── collector/             # 采集器模块
│   ├── __init__.py
│   ├── newspaper_collector.py      # 基础采集器
│   ├── enhanced_collector.py       # 增强采集器
│   └── utils.py                    # 工具函数
├── templates/             # HTML模板
│   ├── base.html
│   ├── index.html
│   ├── newspapers.html
│   ├── add_newspaper.html
│   ├── edit_newspaper.html
│   └── collection_results.html
└── static/                # 静态文件
    ├── css/
    │   └── style.css
    ├── js/
    │   ├── common.js
    │   ├── newspapers.js
    │   ├── add_newspaper.js
    │   └── collection_results.js
    └── images/
```

## 快速开始

### 环境要求
- Python 3.7+
- MySQL 5.7+
- 现代浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd newbaozhi
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**
   - 创建MySQL数据库: `newspaper_db`
   - 修改 `config.py` 中的数据库连接信息

4. **启动应用**
```bash
python run.py
```

5. **访问系统**
   - 打开浏览器访问: http://localhost:5000
   - 系统会自动创建数据库表和示例数据

## 使用指南

### 1. 添加报纸

1. 点击"添加报纸"按钮
2. 填写基本信息：
   - 报纸名称（必填）
   - 报纸URL（必填，支持日期占位符）
   - 省份、城市
   - URL白名单、黑名单

3. 配置采集规则：
   - **版面链接提取规则**: 从报纸首页提取版面链接
   - **版面图片链接提取规则**: 从版面页提取图片链接
   - **版面PDF链接提取规则**: 从版面页提取PDF链接
   - **版面新闻链接提取规则**: 从版面页提取新闻链接

### 2. 采集规则配置

#### 日期占位符
- `YYYY`: 四位年份 (如: 2024)
- `MM`: 两位月份 (如: 01)
- `DD`: 两位日期 (如: 15)

示例URL: `http://paper.people.com.cn/rmrb/html/YYYY-MM/DD/nbs.D110000renmrb_01.htm`

#### 提取方法

**XPath (推荐)**
```xpath
//div[@class="swiper-slide"]//a/@href
//div[@class="pic"]//img/@src
//a[contains(@href, ".pdf")]/@href
```

**正则表达式**
```regex
href="([^"]*\.html)"
src="([^"]*\.jpg)"
href="([^"]*\.pdf)"
```

**BeautifulSoup**
```css
css:.swiper-slide a
attr:img:src
attr:a:href
```

### 3. 测试功能

在添加/编辑报纸页面，可以使用以下测试功能：

- **测试版面链接提取**: 验证能否从首页正确提取版面链接
- **测试版面内容提取**: 验证能否从版面页提取图片、PDF、新闻链接
- **测试完整采集流程**: 完整测试整个采集流程

### 4. 采集操作

- **单份采集**: 在报纸列表页点击"单份采集"按钮
- **批量采集**: 选择多份报纸后点击"批量采集"按钮
- **定时采集**: 在首页点击"开始批量采集"进行全量采集

### 5. 结果管理

在采集结果页面可以：
- 查看采集结果详情
- 筛选和搜索结果
- 更新发布状态
- 导出采集结果

## 配置说明

### 数据库配置 (config.py)
```python
MYSQL_HOST = 'localhost'
MYSQL_PORT = 3306
MYSQL_USER = 'root'
MYSQL_PASSWORD = 'password'
MYSQL_DATABASE = 'newspaper_db'
```

### 采集配置
```python
COLLECTION_TIMEOUT = 30      # 采集超时时间(秒)
MAX_RETRY_TIMES = 3          # 最大重试次数
ITEMS_PER_PAGE = 20          # 每页显示条数
```

## API接口

### 报纸管理
- `GET /api/newspapers` - 获取报纸列表
- `POST /api/newspapers` - 创建报纸
- `GET /api/newspapers/{id}` - 获取报纸详情
- `PUT /api/newspapers/{id}` - 更新报纸
- `DELETE /api/newspapers/{id}` - 删除报纸

### 测试功能
- `POST /api/test/page_links/{id}` - 测试版面链接提取
- `POST /api/test/page_content/{id}` - 测试版面内容提取
- `POST /api/test/full_collection/{id}` - 测试完整采集

### 采集功能
- `POST /api/collection/single/{id}` - 单份采集
- `POST /api/collection/batch` - 批量采集

### 结果管理
- `GET /api/collection/results` - 获取采集结果
- `GET /api/collection/results/{id}` - 获取结果详情
- `PUT /api/collection/results/{id}/publish` - 更新发布状态

### 统计信息
- `GET /api/statistics/dashboard` - 获取首页统计

## 常见问题

### Q: 采集失败怎么办？
A: 
1. 检查网络连接
2. 验证采集规则是否正确
3. 使用测试功能调试规则
4. 查看错误日志

### Q: 如何编写有效的采集规则？
A:
1. 使用浏览器开发者工具分析页面结构
2. 优先使用XPath，语法简洁且功能强大
3. 使用测试功能验证规则
4. 考虑页面结构变化，编写稳定的规则

### Q: 如何处理动态加载的内容？
A: 当前版本不支持JavaScript渲染，建议：
1. 寻找API接口
2. 分析AJAX请求
3. 使用更具体的选择器

## 技术支持

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证。

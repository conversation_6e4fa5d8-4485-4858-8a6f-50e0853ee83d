# -*- coding: utf-8 -*-
"""
采集工具函数
"""
import re
import time
import random
from urllib.parse import urljoin, urlparse
from datetime import datetime

class URLUtils:
    """URL处理工具类"""
    
    @staticmethod
    def replace_date_placeholders(url, target_date=None):
        """替换URL中的日期占位符"""
        if target_date is None:
            target_date = datetime.now()
        
        replacements = {
            'YYYY': target_date.strftime('%Y'),
            'MM': target_date.strftime('%m'),
            'DD': target_date.strftime('%d'),
            'yyyy': target_date.strftime('%Y'),
            'mm': target_date.strftime('%m'),
            'dd': target_date.strftime('%d'),
            'YY': target_date.strftime('%y'),
            'M': str(target_date.month),
            'D': str(target_date.day)
        }
        
        for placeholder, value in replacements.items():
            url = url.replace(placeholder, value)
        
        return url
    
    @staticmethod
    def normalize_url(url, base_url=None):
        """标准化URL"""
        if not url:
            return None
        
        url = url.strip()
        
        # 如果是相对URL，转换为绝对URL
        if base_url and not url.startswith(('http://', 'https://', '//')):
            url = urljoin(base_url, url)
        
        # 处理协议相对URL
        if url.startswith('//'):
            if base_url:
                parsed_base = urlparse(base_url)
                url = f"{parsed_base.scheme}:{url}"
            else:
                url = f"http:{url}"
        
        return url
    
    @staticmethod
    def is_valid_url(url):
        """检查URL是否有效"""
        if not url:
            return False
        
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    @staticmethod
    def filter_urls(urls, whitelist=None, blacklist=None):
        """根据白名单和黑名单过滤URL"""
        if not urls:
            return []
        
        filtered_urls = []
        
        # 解析白名单和黑名单
        whitelist_patterns = []
        blacklist_patterns = []
        
        if whitelist:
            whitelist_patterns = [pattern.strip() for pattern in whitelist.split('\n') if pattern.strip()]
        
        if blacklist:
            blacklist_patterns = [pattern.strip() for pattern in blacklist.split('\n') if pattern.strip()]
        
        for url in urls:
            if not URLUtils.is_valid_url(url):
                continue
            
            # 检查黑名单
            if blacklist_patterns:
                is_blacklisted = False
                for pattern in blacklist_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        is_blacklisted = True
                        break
                if is_blacklisted:
                    continue
            
            # 检查白名单
            if whitelist_patterns:
                is_whitelisted = False
                for pattern in whitelist_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        is_whitelisted = True
                        break
                if not is_whitelisted:
                    continue
            
            filtered_urls.append(url)
        
        return filtered_urls

class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, min_delay=1, max_delay=3):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request_time = 0
    
    def wait(self):
        """等待适当的时间间隔"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        # 随机延迟时间
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if elapsed < delay:
            time.sleep(delay - elapsed)
        
        self.last_request_time = time.time()

class ContentCleaner:
    """内容清理工具"""
    
    @staticmethod
    def clean_text(text):
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 移除HTML实体
        html_entities = {
            '&nbsp;': ' ',
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&#39;': "'",
            '&hellip;': '...'
        }
        
        for entity, replacement in html_entities.items():
            text = text.replace(entity, replacement)
        
        return text
    
    @staticmethod
    def extract_title_from_url(url):
        """从URL中提取可能的标题"""
        if not url:
            return None
        
        try:
            parsed = urlparse(url)
            path = parsed.path
            
            # 移除文件扩展名
            if '.' in path:
                path = path.rsplit('.', 1)[0]
            
            # 提取最后一部分作为标题
            parts = path.split('/')
            for part in reversed(parts):
                if part and not part.isdigit():
                    return part.replace('-', ' ').replace('_', ' ')
            
            return None
        except:
            return None

class ValidationUtils:
    """验证工具"""
    
    @staticmethod
    def validate_extraction_rule(rule, method):
        """验证提取规则的语法"""
        if not rule:
            return True, "规则为空"
        
        try:
            if method == 're':
                re.compile(rule)
            elif method == 'xpath':
                # 简单的XPath语法检查
                if not rule.startswith(('/', './', '//')):
                    return False, "XPath规则应该以 '/', './' 或 '//' 开头"
            elif method == 'bs4':
                # BeautifulSoup选择器语法检查
                pass  # BS4的选择器比较灵活，这里不做严格检查
            else:
                return False, f"不支持的提取方法: {method}"
            
            return True, "规则语法正确"
        except re.error as e:
            return False, f"正则表达式语法错误: {str(e)}"
        except Exception as e:
            return False, f"规则验证失败: {str(e)}"
    
    @staticmethod
    def validate_newspaper_config(config):
        """验证报纸配置"""
        errors = []
        
        # 检查必填字段
        if not config.get('name'):
            errors.append("报纸名称不能为空")
        
        if not config.get('url'):
            errors.append("报纸URL不能为空")
        
        # 检查URL格式
        if config.get('url') and not URLUtils.is_valid_url(config['url']):
            # 如果包含日期占位符，先替换再检查
            test_url = URLUtils.replace_date_placeholders(config['url'])
            if not URLUtils.is_valid_url(test_url):
                errors.append("报纸URL格式不正确")
        
        # 检查提取规则
        rules = [
            ('page_link_rule', 'page_link_method'),
            ('image_link_rule', 'image_link_method'),
            ('pdf_link_rule', 'pdf_link_method'),
            ('news_link_rule', 'news_link_method')
        ]
        
        for rule_key, method_key in rules:
            rule = config.get(rule_key)
            method = config.get(method_key, 'xpath')
            
            if rule:
                is_valid, message = ValidationUtils.validate_extraction_rule(rule, method)
                if not is_valid:
                    errors.append(f"{rule_key}: {message}")
        
        return len(errors) == 0, errors

class LogUtils:
    """日志工具"""
    
    @staticmethod
    def log_collection_start(newspaper_name):
        """记录采集开始"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] 开始采集报纸: {newspaper_name}")
    
    @staticmethod
    def log_collection_end(newspaper_name, success, duration, message=""):
        """记录采集结束"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        status = "成功" if success else "失败"
        print(f"[{timestamp}] 采集{status}: {newspaper_name}, 耗时: {duration}秒, {message}")
    
    @staticmethod
    def log_page_extraction(page_url, success, counts=None):
        """记录页面提取"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if success and counts:
            print(f"[{timestamp}] 页面提取成功: {page_url}, "
                  f"图片: {counts.get('images', 0)}, "
                  f"PDF: {counts.get('pdfs', 0)}, "
                  f"新闻: {counts.get('news', 0)}")
        else:
            print(f"[{timestamp}] 页面提取失败: {page_url}")
    
    @staticmethod
    def log_error(message, exception=None):
        """记录错误"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        error_msg = f"[{timestamp}] 错误: {message}"
        if exception:
            error_msg += f", 异常: {str(exception)}"
        print(error_msg)

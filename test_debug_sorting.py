#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试排序调试
"""
import requests

def test_debug_sorting():
    """测试排序调试"""
    print('测试排序参数调试:')
    r = requests.get('http://127.0.0.1:5009/api/newspapers')
    if r.status_code == 200:
        data = r.json()
        if data['success']:
            newspapers = data['data']
            print(f'返回 {len(newspapers)} 个报纸')
            for i, n in enumerate(newspapers, 1):
                name = n['name']
                newspaper_id = n['id']
                created_at = n['created_at']
                print(f'{i}. {name} (ID: {newspaper_id}) - {created_at}')
        else:
            print(f'API错误: {data.get("message")}')
    else:
        print(f'HTTP错误: {r.status_code}')

if __name__ == "__main__":
    test_debug_sorting()

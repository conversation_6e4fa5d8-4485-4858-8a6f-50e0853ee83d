#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试提取规则API
"""
import requests
import json

def test_extraction_rules_api():
    """测试提取规则API"""
    base_url = "http://127.0.0.1:5009"
    
    print("测试提取规则API...")
    print("=" * 50)
    
    try:
        response = requests.get(
            f"{base_url}/api/extraction_rules",
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                
                print("✅ API调用成功!")
                print(f"版面链接规则数量: {len(data.get('page_link_rules', []))}")
                print(f"图片链接规则数量: {len(data.get('image_link_rules', []))}")
                print(f"PDF链接规则数量: {len(data.get('pdf_link_rules', []))}")
                print(f"新闻链接规则数量: {len(data.get('news_link_rules', []))}")
                
                # 显示版面链接规则示例
                if data.get('page_link_rules'):
                    print("\n📄 版面链接规则示例:")
                    for i, rule in enumerate(data['page_link_rules'][:3], 1):
                        print(f"  {i}. 规则: {rule['rule'][:50]}{'...' if len(rule['rule']) > 50 else ''}")
                        print(f"     方法: {rule['method']}")
                        print(f"     来源: {rule['newspaper_name']}")
                        print()
                
                # 显示图片链接规则示例
                if data.get('image_link_rules'):
                    print("🖼️ 图片链接规则示例:")
                    for i, rule in enumerate(data['image_link_rules'][:3], 1):
                        print(f"  {i}. 规则: {rule['rule'][:50]}{'...' if len(rule['rule']) > 50 else ''}")
                        print(f"     方法: {rule['method']}")
                        print(f"     来源: {rule['newspaper_name']}")
                        print()
                
                # 显示PDF链接规则示例
                if data.get('pdf_link_rules'):
                    print("📋 PDF链接规则示例:")
                    for i, rule in enumerate(data['pdf_link_rules'][:3], 1):
                        print(f"  {i}. 规则: {rule['rule'][:50]}{'...' if len(rule['rule']) > 50 else ''}")
                        print(f"     方法: {rule['method']}")
                        print(f"     来源: {rule['newspaper_name']}")
                        print()
                
                # 显示新闻链接规则示例
                if data.get('news_link_rules'):
                    print("📰 新闻链接规则示例:")
                    for i, rule in enumerate(data['news_link_rules'][:3], 1):
                        print(f"  {i}. 规则: {rule['rule'][:50]}{'...' if len(rule['rule']) > 50 else ''}")
                        print(f"     方法: {rule['method']}")
                        print(f"     来源: {rule['newspaper_name']}")
                        print()
                
            else:
                print(f"❌ API返回失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print("响应内容:", response.text[:200])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    test_extraction_rules_api()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试报纸管理页面排序功能
"""
import requests
import json

def test_newspaper_sorting():
    """测试报纸管理页面排序功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("📊 测试报纸管理页面排序功能")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/newspapers", timeout=10)
        if response.status_code == 200:
            print("   ✅ 报纸管理页面可访问")
            
            # 检查排序相关的HTML元素
            content = response.text
            
            sorting_features = [
                ('排序', '排序按钮'),
                ('最新添加在前', '添加时间倒序选项'),
                ('最早添加在前', '添加时间正序选项'),
                ('最近采集在前', '采集时间倒序选项'),
                ('最早采集在前', '采集时间正序选项'),
                ('A-Z排序', '名称正序选项'),
                ('Z-A排序', '名称倒序选项'),
                ('current-sort-info', '当前排序信息显示'),
                ('sortable', '可排序表头'),
                ('toggleSort', '表头点击排序'),
                ('setSorting', '设置排序函数')
            ]
            
            print("   🔍 检查排序功能元素:")
            for feature, description in sorting_features:
                if feature in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript排序功能
    print("\n2️⃣ 测试JavaScript排序功能...")
    try:
        response = requests.get(f"{base_url}/static/js/newspapers.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查排序相关的JavaScript功能
            js_features = [
                ('currentSort', '当前排序状态变量'),
                ('setSorting', '设置排序函数'),
                ('toggleSort', '切换排序函数'),
                ('updateSortInfo', '更新排序信息函数'),
                ('updateSortIcons', '更新排序图标函数'),
                ('sort_by', '排序字段参数'),
                ('sort_order', '排序方向参数'),
                ('fa-sort-up', '正序图标'),
                ('fa-sort-down', '倒序图标'),
                ('created_at', '创建时间排序'),
                ('last_collection_time', '最后采集时间排序')
            ]
            
            print("   🔧 检查JavaScript排序功能:")
            for feature_code, description in js_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API排序功能
    print("\n3️⃣ 测试API排序功能...")
    
    # 测试不同的排序参数
    sort_tests = [
        {
            'name': '默认排序（添加时间倒序）',
            'params': {},
            'expected_order': 'created_at desc'
        },
        {
            'name': '添加时间正序',
            'params': {'sort_by': 'created_at', 'sort_order': 'asc'},
            'expected_order': 'created_at asc'
        },
        {
            'name': '最后采集时间倒序',
            'params': {'sort_by': 'last_collection_time', 'sort_order': 'desc'},
            'expected_order': 'last_collection_time desc'
        },
        {
            'name': '最后采集时间正序',
            'params': {'sort_by': 'last_collection_time', 'sort_order': 'asc'},
            'expected_order': 'last_collection_time asc'
        },
        {
            'name': '报纸名称A-Z',
            'params': {'sort_by': 'name', 'sort_order': 'asc'},
            'expected_order': 'name asc'
        },
        {
            'name': '报纸名称Z-A',
            'params': {'sort_by': 'name', 'sort_order': 'desc'},
            'expected_order': 'name desc'
        }
    ]
    
    for test_case in sort_tests:
        print(f"\n   🧪 {test_case['name']}:")
        try:
            response = requests.get(
                f"{base_url}/api/newspapers",
                params=test_case['params'],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    newspapers = result.get('data', [])
                    print(f"   ✅ API请求成功，返回 {len(newspapers)} 个报纸")
                    
                    # 验证排序效果（如果有数据的话）
                    if len(newspapers) >= 2:
                        first = newspapers[0]
                        second = newspapers[1]
                        
                        if 'created_at' in test_case['params'].get('sort_by', 'created_at'):
                            print(f"   📅 第一个: {first.get('name')} (ID: {first.get('id')})")
                            print(f"   📅 第二个: {second.get('name')} (ID: {second.get('id')})")
                        elif 'last_collection_time' in test_case['params'].get('sort_by', ''):
                            print(f"   🕒 第一个: {first.get('name')} - {first.get('last_collection_time', 'None')}")
                            print(f"   🕒 第二个: {second.get('name')} - {second.get('last_collection_time', 'None')}")
                        elif 'name' in test_case['params'].get('sort_by', ''):
                            print(f"   📝 第一个: {first.get('name')}")
                            print(f"   📝 第二个: {second.get('name')}")
                    else:
                        print(f"   ℹ️ 数据不足，无法验证排序效果")
                        
                else:
                    print(f"   ❌ API返回错误: {result.get('message')}")
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 测试请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 报纸管理页面排序功能测试完成！")
    
    print("\n📋 排序功能特性:")
    print("✅ 默认排序 - 按添加时间倒序，最新添加的报纸在前")
    print("✅ 添加时间排序 - 支持正序和倒序")
    print("✅ 最后采集时间排序 - 支持正序和倒序，NULL值处理")
    print("✅ 报纸名称排序 - 支持A-Z和Z-A排序")
    print("✅ 表头点击排序 - 点击表头可快速切换排序")
    print("✅ 下拉菜单排序 - 提供完整的排序选项")
    print("✅ 排序状态显示 - 实时显示当前排序状态")
    print("✅ 排序图标 - 表头显示排序方向图标")
    
    print("\n🎯 排序选项:")
    print("📅 按添加时间:")
    print("   • 最新添加在前 (默认)")
    print("   • 最早添加在前")
    print("🕒 按最后采集时间:")
    print("   • 最近采集在前")
    print("   • 最早采集在前")
    print("📝 按报纸名称:")
    print("   • A-Z排序")
    print("   • Z-A排序")
    
    print("\n🎨 用户界面:")
    print("- 排序下拉菜单：提供所有排序选项")
    print("- 可点击表头：快速切换排序字段和方向")
    print("- 排序状态显示：显示当前排序方式")
    print("- 排序图标：表头显示排序方向")
    
    print("\n🚀 使用方法:")
    print("1. 点击排序下拉菜单选择排序方式")
    print("2. 点击表头'报纸名称'或'最后采集时间'快速排序")
    print("3. 观察排序状态显示和图标变化")
    print("4. 验证报纸列表的排序效果")
    
    print("\n💡 技术特点:")
    print("- 后端SQL排序：性能优化，支持大数据量")
    print("- NULL值处理：采集时间为空的报纸合理排序")
    print("- 状态保持：排序状态在筛选和分页时保持")
    print("- 用户友好：多种排序方式，操作简单直观")
    
    return True

if __name__ == "__main__":
    test_newspaper_sorting()

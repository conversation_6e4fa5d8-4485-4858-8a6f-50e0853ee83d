# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""
from app import app
from database import db
from models import Newspaper, CollectionRule, CollectionResult, CollectionTask

def init_database():
    """初始化数据库"""
    with app.app_context():
        # 删除所有表
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        print("数据库初始化完成！")
        
        # 创建示例数据
        create_sample_data()

def create_sample_data():
    """创建示例数据"""
    # 示例报纸数据
    sample_newspapers = [
        {
            'name': '人民日报',
            'url': 'http://paper.people.com.cn/rmrb/html/YYYY-MM/DD/nbs.D110000renmrb_01.htm',
            'province': '北京',
            'city': '北京',
            'page_link_rule': '//div[@class="swiper-slide"]//a/@href',
            'page_link_method': 'xpath',
            'image_link_rule': '//div[@class="pic"]//img/@src',
            'image_link_method': 'xpath',
            'pdf_link_rule': '//a[contains(@href, ".pdf")]/@href',
            'pdf_link_method': 'xpath',
            'news_link_rule': '//div[@class="news"]//a/@href',
            'news_link_method': 'xpath'
        },
        {
            'name': '光明日报',
            'url': 'http://epaper.gmw.cn/gmrb/html/YYYY-MM/DD/nbs.D110000gmrb_01.htm',
            'province': '北京',
            'city': '北京',
            'page_link_rule': '//div[@class="ban_list_nav"]//a/@href',
            'page_link_method': 'xpath',
            'image_link_rule': '//div[@class="pic"]//img/@src',
            'image_link_method': 'xpath',
            'pdf_link_rule': '//a[contains(text(), "PDF")]/@href',
            'pdf_link_method': 'xpath',
            'news_link_rule': '//div[@class="news_list"]//a/@href',
            'news_link_method': 'xpath'
        }
    ]
    
    for newspaper_data in sample_newspapers:
        newspaper = Newspaper(**newspaper_data)
        db.session.add(newspaper)
    
    try:
        db.session.commit()
        print("示例数据创建完成！")
    except Exception as e:
        db.session.rollback()
        print(f"创建示例数据失败: {e}")

if __name__ == '__main__':
    init_database()

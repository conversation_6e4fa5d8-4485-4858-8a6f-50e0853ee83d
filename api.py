# -*- coding: utf-8 -*-
"""
API接口定义
"""
from flask import Blueprint, request, jsonify
from database import db
from models import Newspaper, CollectionRule, CollectionResult, CollectionTask
from datetime import datetime, date
import json
import requests
from collector.newspaper_collector import NewspaperCollector

# 创建API蓝图
api = Blueprint('api', __name__, url_prefix='/api')

@api.route('/newspapers', methods=['GET'])
def get_newspapers():
    """获取报纸列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 筛选参数
        name = request.args.get('name', '')
        province = request.args.get('province', '')
        city = request.args.get('city', '')
        collection_status = request.args.get('collection_status', '')
        publish_status = request.args.get('publish_status', '')
        
        # 构建查询
        query = Newspaper.query
        
        if name:
            query = query.filter(Newspaper.name.like(f'%{name}%'))
        if province:
            query = query.filter(Newspaper.province == province)
        if city:
            query = query.filter(Newspaper.city == city)
        if collection_status:
            query = query.filter(Newspaper.collection_status == collection_status)
        if publish_status:
            query = query.filter(Newspaper.publish_status == publish_status)
        
        # 分页查询
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        newspapers = [newspaper.to_dict() for newspaper in pagination.items]
        
        return jsonify({
            'success': True,
            'data': newspapers,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/newspapers', methods=['POST'])
def create_newspaper():
    """创建报纸"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name') or not data.get('url'):
            return jsonify({'success': False, 'message': '报纸名称和URL不能为空'}), 400
        
        # 检查报纸名称是否已存在
        existing = Newspaper.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({'success': False, 'message': '报纸名称已存在'}), 400
        
        newspaper = Newspaper(
            name=data['name'],
            url=data['url'],
            province=data.get('province', ''),
            city=data.get('city', ''),
            url_whitelist=data.get('url_whitelist', ''),
            url_blacklist=data.get('url_blacklist', ''),
            page_link_rule=data.get('page_link_rule', ''),
            page_link_method=data.get('page_link_method', 'xpath'),
            image_link_rule=data.get('image_link_rule', ''),
            image_link_method=data.get('image_link_method', 'xpath'),
            pdf_link_rule=data.get('pdf_link_rule', ''),
            pdf_link_method=data.get('pdf_link_method', 'xpath'),
            news_link_rule=data.get('news_link_rule', ''),
            news_link_method=data.get('news_link_method', 'xpath')
        )
        
        db.session.add(newspaper)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '报纸创建成功',
            'data': newspaper.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/newspapers/<int:id>', methods=['GET'])
def get_newspaper(id):
    """获取单个报纸信息"""
    try:
        newspaper = Newspaper.query.get_or_404(id)
        return jsonify({
            'success': True,
            'data': newspaper.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/newspapers/<int:id>', methods=['PUT'])
def update_newspaper(id):
    """更新报纸信息"""
    try:
        newspaper = Newspaper.query.get_or_404(id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查名称是否重复
            existing = Newspaper.query.filter(
                Newspaper.name == data['name'],
                Newspaper.id != id
            ).first()
            if existing:
                return jsonify({'success': False, 'message': '报纸名称已存在'}), 400
            newspaper.name = data['name']
        
        if 'url' in data:
            newspaper.url = data['url']
        if 'province' in data:
            newspaper.province = data['province']
        if 'city' in data:
            newspaper.city = data['city']
        if 'url_whitelist' in data:
            newspaper.url_whitelist = data['url_whitelist']
        if 'url_blacklist' in data:
            newspaper.url_blacklist = data['url_blacklist']
        if 'page_link_rule' in data:
            newspaper.page_link_rule = data['page_link_rule']
        if 'page_link_method' in data:
            newspaper.page_link_method = data['page_link_method']
        if 'image_link_rule' in data:
            newspaper.image_link_rule = data['image_link_rule']
        if 'image_link_method' in data:
            newspaper.image_link_method = data['image_link_method']
        if 'pdf_link_rule' in data:
            newspaper.pdf_link_rule = data['pdf_link_rule']
        if 'pdf_link_method' in data:
            newspaper.pdf_link_method = data['pdf_link_method']
        if 'news_link_rule' in data:
            newspaper.news_link_rule = data['news_link_rule']
        if 'news_link_method' in data:
            newspaper.news_link_method = data['news_link_method']
        
        newspaper.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '报纸更新成功',
            'data': newspaper.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/newspapers/<int:id>', methods=['DELETE'])
def delete_newspaper(id):
    """删除报纸"""
    try:
        newspaper = Newspaper.query.get_or_404(id)
        
        # 删除相关的采集结果
        CollectionResult.query.filter_by(newspaper_id=id).delete()
        
        db.session.delete(newspaper)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '报纸删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/test/page_links/<int:id>', methods=['POST'])
def test_page_links(id):
    """测试版面链接提取规则"""
    try:
        if id == 0:
            # 临时测试，从请求体获取数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请提供测试数据'}), 400

            # 创建临时报纸对象
            class TempNewspaper:
                def __init__(self, data):
                    self.name = data.get('name', '测试报纸')
                    self.url = data.get('url', '')
                    self.page_link_rule = data.get('page_link_rule', '')
                    self.page_link_method = data.get('page_link_method', 'xpath')

            newspaper = TempNewspaper(data)
        else:
            newspaper = Newspaper.query.get_or_404(id)

        if not newspaper.page_link_rule:
            return jsonify({'success': False, 'message': '版面链接提取规则未配置'}), 400

        collector = NewspaperCollector()
        result = collector.test_page_links(newspaper)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/test/two_links/<int:id>', methods=['POST'])
def test_two_links(id):
    """测试双链接提取规则（版面链接 + 图片/PDF链接）"""
    try:
        if id == 0:
            # 临时测试，从请求体获取数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请提供测试数据'}), 400

            # 创建临时报纸对象
            class TempNewspaper:
                def __init__(self, data):
                    self.name = data.get('name', '测试报纸')
                    self.url = data.get('url', '')
                    self.page_link_rule = data.get('page_link_rule', '')
                    self.page_link_method = data.get('page_link_method', 'xpath')

            newspaper = TempNewspaper(data)
        else:
            newspaper = Newspaper.query.get_or_404(id)

        if not newspaper.page_link_rule:
            return jsonify({'success': False, 'message': '版面链接提取规则未配置'}), 400

        collector = NewspaperCollector()
        result = collector.test_two_links(newspaper)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/test/three_links/<int:id>', methods=['POST'])
def test_three_links(id):
    """测试三链接提取规则（版面链接 + 图片链接 + PDF链接）"""
    try:
        if id == 0:
            # 临时测试，从请求体获取数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请提供测试数据'}), 400

            # 创建临时报纸对象
            class TempNewspaper:
                def __init__(self, data):
                    self.name = data.get('name', '测试报纸')
                    self.url = data.get('url', '')
                    self.page_link_rule = data.get('page_link_rule', '')
                    self.page_link_method = data.get('page_link_method', 'xpath')

            newspaper = TempNewspaper(data)
        else:
            newspaper = Newspaper.query.get_or_404(id)

        if not newspaper.page_link_rule:
            return jsonify({'success': False, 'message': '版面链接提取规则未配置'}), 400

        collector = NewspaperCollector()
        result = collector.test_three_links(newspaper)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/test/page_content/<int:id>', methods=['POST'])
def test_page_content(id):
    """测试版面内容提取规则"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请提供测试数据'}), 400

        if id == 0:
            # 临时测试，从请求体获取数据
            class TempNewspaper:
                def __init__(self, data):
                    self.name = data.get('name', '测试报纸')
                    self.url = data.get('url', '')
                    self.image_link_rule = data.get('image_link_rule', '')
                    self.image_link_method = data.get('image_link_method', 'xpath')
                    self.pdf_link_rule = data.get('pdf_link_rule', '')
                    self.pdf_link_method = data.get('pdf_link_method', 'xpath')
                    self.news_link_rule = data.get('news_link_rule', '')
                    self.news_link_method = data.get('news_link_method', 'xpath')

            newspaper = TempNewspaper(data)
        else:
            newspaper = Newspaper.query.get_or_404(id)

        page_url = data.get('page_url')
        if not page_url:
            return jsonify({'success': False, 'message': '版面URL不能为空'}), 400

        collector = NewspaperCollector()
        result = collector.test_page_content(newspaper, page_url)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/test/full_collection/<int:id>', methods=['POST'])
def test_full_collection(id):
    """测试完整采集流程"""
    try:
        if id == 0:
            # 临时测试，从请求体获取数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请提供测试数据'}), 400

            class TempNewspaper:
                def __init__(self, data):
                    self.name = data.get('name', '测试报纸')
                    self.url = data.get('url', '')
                    self.page_link_rule = data.get('page_link_rule', '')
                    self.page_link_method = data.get('page_link_method', 'xpath')
                    self.image_link_rule = data.get('image_link_rule', '')
                    self.image_link_method = data.get('image_link_method', 'xpath')
                    self.pdf_link_rule = data.get('pdf_link_rule', '')
                    self.pdf_link_method = data.get('pdf_link_method', 'xpath')
                    self.news_link_rule = data.get('news_link_rule', '')
                    self.news_link_method = data.get('news_link_method', 'xpath')

            newspaper = TempNewspaper(data)
        else:
            newspaper = Newspaper.query.get_or_404(id)

        collector = NewspaperCollector()
        result = collector.test_full_collection(newspaper)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/collection/single/<int:id>', methods=['POST'])
def collect_single_newspaper(id):
    """单份报纸采集"""
    try:
        newspaper = Newspaper.query.get_or_404(id)

        # 更新采集状态
        newspaper.collection_status = '采集中'
        newspaper.last_collection_time = datetime.utcnow()
        db.session.commit()

        collector = NewspaperCollector()
        result = collector.collect_newspaper(newspaper)

        # 更新采集状态
        if result['success']:
            newspaper.collection_status = '采集成功'

            # 保存采集结果
            for page_data in result['data']:
                collection_result = CollectionResult(
                    newspaper_id=newspaper.id,
                    newspaper_name=newspaper.name,
                    newspaper_url=newspaper.url,
                    province=newspaper.province,
                    city=newspaper.city,
                    page_link=page_data.get('page_link'),
                    page_title=page_data.get('page_title'),
                    image_link=page_data.get('image_link'),
                    pdf_link=page_data.get('pdf_link'),
                    news_links=json.dumps(page_data.get('news_links', [])),
                    news_count=len(page_data.get('news_links', [])),
                    collection_time=datetime.utcnow(),
                    collection_date=date.today()
                )
                db.session.add(collection_result)
        else:
            newspaper.collection_status = '采集失败'

        newspaper.collection_duration = result.get('duration', 0)
        db.session.commit()

        return jsonify(result)
    except Exception as e:
        # 更新采集状态为失败
        newspaper.collection_status = '采集失败'
        db.session.commit()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/collection/batch', methods=['POST'])
def collect_batch_newspapers():
    """批量报纸采集"""
    try:
        data = request.get_json()
        newspaper_ids = data.get('newspaper_ids', [])

        if not newspaper_ids:
            return jsonify({'success': False, 'message': '请选择要采集的报纸'}), 400

        # 创建采集任务
        task = CollectionTask(
            task_name=f"批量采集_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            task_type='batch',
            newspaper_ids=json.dumps(newspaper_ids),
            total_count=len(newspaper_ids),
            status='执行中',
            start_time=datetime.utcnow()
        )
        db.session.add(task)
        db.session.commit()

        collector = NewspaperCollector()
        results = []
        success_count = 0
        failed_count = 0

        for newspaper_id in newspaper_ids:
            newspaper = Newspaper.query.get(newspaper_id)
            if not newspaper:
                continue

            try:
                result = collector.collect_newspaper(newspaper)
                if result['success']:
                    success_count += 1
                    # 保存采集结果
                    for page_data in result['data']:
                        collection_result = CollectionResult(
                            newspaper_id=newspaper.id,
                            newspaper_name=newspaper.name,
                            newspaper_url=newspaper.url,
                            province=newspaper.province,
                            city=newspaper.city,
                            page_link=page_data.get('page_link'),
                            page_title=page_data.get('page_title'),
                            image_link=page_data.get('image_link'),
                            pdf_link=page_data.get('pdf_link'),
                            news_links=json.dumps(page_data.get('news_links', [])),
                            news_count=len(page_data.get('news_links', [])),
                            collection_time=datetime.utcnow(),
                            collection_date=date.today()
                        )
                        db.session.add(collection_result)
                else:
                    failed_count += 1

                results.append({
                    'newspaper_id': newspaper_id,
                    'newspaper_name': newspaper.name,
                    'success': result['success'],
                    'message': result.get('message', '')
                })

                # 更新报纸采集状态
                newspaper.collection_status = '采集成功' if result['success'] else '采集失败'
                newspaper.last_collection_time = datetime.utcnow()
                newspaper.collection_duration = result.get('duration', 0)

            except Exception as e:
                failed_count += 1
                results.append({
                    'newspaper_id': newspaper_id,
                    'newspaper_name': newspaper.name if newspaper else '未知',
                    'success': False,
                    'message': str(e)
                })

        # 更新任务状态
        task.end_time = datetime.utcnow()
        task.duration = int((task.end_time - task.start_time).total_seconds())
        task.success_count = success_count
        task.failed_count = failed_count
        task.status = '已完成'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'批量采集完成，成功{success_count}个，失败{failed_count}个',
            'data': {
                'task_id': task.id,
                'results': results,
                'summary': {
                    'total': len(newspaper_ids),
                    'success': success_count,
                    'failed': failed_count
                }
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/collection/results', methods=['GET'])
def get_collection_results():
    """获取采集结果列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 筛选参数
        newspaper_name = request.args.get('newspaper_name', '')
        province = request.args.get('province', '')
        city = request.args.get('city', '')
        collection_date = request.args.get('collection_date', '')
        publish_status = request.args.get('publish_status', '')

        # 构建查询
        query = CollectionResult.query

        if newspaper_name:
            query = query.filter(CollectionResult.newspaper_name.like(f'%{newspaper_name}%'))
        if province:
            query = query.filter(CollectionResult.province == province)
        if city:
            query = query.filter(CollectionResult.city == city)
        if collection_date:
            query = query.filter(CollectionResult.collection_date == collection_date)
        if publish_status:
            query = query.filter(CollectionResult.publish_status == publish_status)

        # 按采集时间倒序排列
        query = query.order_by(CollectionResult.collection_time.desc())

        # 分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        results = [result.to_dict() for result in pagination.items]

        return jsonify({
            'success': True,
            'data': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/collection/results/<int:id>', methods=['GET'])
def get_collection_result(id):
    """获取单个采集结果详情"""
    try:
        result = CollectionResult.query.get_or_404(id)
        return jsonify({
            'success': True,
            'data': result.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/collection/results/<int:id>/publish', methods=['PUT'])
def update_publish_status(id):
    """更新发布状态"""
    try:
        result = CollectionResult.query.get_or_404(id)
        data = request.get_json()

        publish_status = data.get('publish_status')
        if publish_status not in ['已发布', '未发布']:
            return jsonify({'success': False, 'message': '发布状态值无效'}), 400

        result.publish_status = publish_status
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '发布状态更新成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/statistics/dashboard', methods=['GET'])
def get_dashboard_statistics():
    """获取首页统计数据"""
    try:
        today = date.today()

        # 总报纸数
        total_newspapers = Newspaper.query.filter_by(is_active=True).count()

        # 当天已采集报纸数
        today_collected = Newspaper.query.filter(
            Newspaper.last_collection_time >= today,
            Newspaper.collection_status == '采集成功'
        ).count()

        # 当天未采集报纸数
        today_not_collected = total_newspapers - today_collected

        # 当天采集结果数
        today_results = CollectionResult.query.filter_by(collection_date=today).count()

        # 当天发布数据
        today_published = CollectionResult.query.filter(
            CollectionResult.collection_date == today,
            CollectionResult.publish_status == '已发布'
        ).count()

        # 省份统计
        province_stats = db.session.query(
            Newspaper.province,
            db.func.count(Newspaper.id).label('count')
        ).filter(Newspaper.is_active == True).group_by(Newspaper.province).all()

        return jsonify({
            'success': True,
            'data': {
                'total_newspapers': total_newspapers,
                'today_collected': today_collected,
                'today_not_collected': today_not_collected,
                'today_results': today_results,
                'today_published': today_published,
                'province_stats': [{'province': p[0], 'count': p[1]} for p in province_stats]
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api.route('/extraction_rules', methods=['GET'])
def get_extraction_rules():
    """获取已有的提取规则"""
    try:
        # 获取所有报纸的提取规则
        newspapers = Newspaper.query.all()

        rules = {
            'page_link_rules': [],
            'image_link_rules': [],
            'pdf_link_rules': [],
            'news_link_rules': []
        }

        # 收集所有不重复的规则（只按规则内容排重，不考虑方法）
        page_rules_set = set()
        image_rules_set = set()
        pdf_rules_set = set()
        news_rules_set = set()

        for newspaper in newspapers:
            # 版面链接规则
            if newspaper.page_link_rule and newspaper.page_link_rule.strip():
                rule_content = newspaper.page_link_rule.strip()
                if rule_content not in page_rules_set:
                    page_rules_set.add(rule_content)
                    rules['page_link_rules'].append({
                        'rule': rule_content,
                        'method': newspaper.page_link_method,
                        'newspaper_name': newspaper.name
                    })

            # 图片链接规则
            if newspaper.image_link_rule and newspaper.image_link_rule.strip():
                rule_content = newspaper.image_link_rule.strip()
                if rule_content not in image_rules_set:
                    image_rules_set.add(rule_content)
                    rules['image_link_rules'].append({
                        'rule': rule_content,
                        'method': newspaper.image_link_method,
                        'newspaper_name': newspaper.name
                    })

            # PDF链接规则
            if newspaper.pdf_link_rule and newspaper.pdf_link_rule.strip():
                rule_content = newspaper.pdf_link_rule.strip()
                if rule_content not in pdf_rules_set:
                    pdf_rules_set.add(rule_content)
                    rules['pdf_link_rules'].append({
                        'rule': rule_content,
                        'method': newspaper.pdf_link_method,
                        'newspaper_name': newspaper.name
                    })

            # 新闻链接规则
            if newspaper.news_link_rule and newspaper.news_link_rule.strip():
                rule_content = newspaper.news_link_rule.strip()
                if rule_content not in news_rules_set:
                    news_rules_set.add(rule_content)
                    rules['news_link_rules'].append({
                        'rule': rule_content,
                        'method': newspaper.news_link_method,
                        'newspaper_name': newspaper.name
                    })

        return jsonify({
            'success': True,
            'data': rules
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

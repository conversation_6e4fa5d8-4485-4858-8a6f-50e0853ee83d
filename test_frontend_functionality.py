#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试前端下拉框功能
"""
import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_frontend_functionality():
    """测试前端下拉框功能"""
    print("测试前端下拉框功能...")
    print("=" * 50)
    
    # 首先测试API是否正常
    try:
        response = requests.get("http://127.0.0.1:5009/api/extraction_rules", timeout=5)
        if response.status_code != 200:
            print("❌ API不可用，跳过前端测试")
            return
        print("✅ API正常")
    except:
        print("❌ 无法连接到服务器，跳过前端测试")
        return
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = None
    try:
        # 启动浏览器
        print("启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # 访问添加报纸页面
        print("访问添加报纸页面...")
        driver.get("http://127.0.0.1:5009/add_newspaper")
        
        # 等待页面加载完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "page_link_rule_select"))
        )
        print("✅ 页面加载完成")
        
        # 等待AJAX请求完成（加载提取规则）
        time.sleep(3)
        
        # 测试版面链接规则下拉框
        print("\n测试版面链接规则下拉框...")
        page_select = Select(driver.find_element(By.ID, "page_link_rule_select"))
        options = page_select.options
        print(f"  下拉框选项数量: {len(options)}")
        
        if len(options) > 1:  # 除了默认的"选择已有规则..."选项
            print("  ✅ 下拉框有可选规则")
            # 选择第一个规则
            page_select.select_by_index(1)
            time.sleep(1)
            
            # 检查是否自动填充到文本框
            rule_textarea = driver.find_element(By.ID, "page_link_rule")
            if rule_textarea.get_attribute("value"):
                print("  ✅ 规则自动填充成功")
            else:
                print("  ❌ 规则未自动填充")
        else:
            print("  ⚠️ 下拉框无可选规则")
        
        # 测试图片链接规则下拉框
        print("\n测试图片链接规则下拉框...")
        image_select = Select(driver.find_element(By.ID, "image_link_rule_select"))
        options = image_select.options
        print(f"  下拉框选项数量: {len(options)}")
        
        if len(options) > 1:
            print("  ✅ 下拉框有可选规则")
        else:
            print("  ⚠️ 下拉框无可选规则")
        
        # 测试PDF链接规则下拉框
        print("\n测试PDF链接规则下拉框...")
        pdf_select = Select(driver.find_element(By.ID, "pdf_link_rule_select"))
        options = pdf_select.options
        print(f"  下拉框选项数量: {len(options)}")
        
        if len(options) > 1:
            print("  ✅ 下拉框有可选规则")
        else:
            print("  ⚠️ 下拉框无可选规则")
        
        # 测试新闻链接规则下拉框
        print("\n测试新闻链接规则下拉框...")
        news_select = Select(driver.find_element(By.ID, "news_link_rule_select"))
        options = news_select.options
        print(f"  下拉框选项数量: {len(options)}")
        
        if len(options) > 1:
            print("  ✅ 下拉框有可选规则")
        else:
            print("  ⚠️ 下拉框无可选规则")
        
        print("\n✅ 前端功能测试完成")
        
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        
    finally:
        if driver:
            driver.quit()
    
    print("=" * 50)

def simple_test():
    """简单测试（不使用Selenium）"""
    print("执行简单测试...")
    print("=" * 50)
    
    # 测试页面是否可访问
    try:
        response = requests.get("http://127.0.0.1:5009/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("✅ 添加报纸页面可访问")
            
            # 检查页面是否包含新的下拉框元素
            content = response.text
            if 'id="page_link_rule_select"' in content:
                print("✅ 版面链接规则下拉框存在")
            else:
                print("❌ 版面链接规则下拉框不存在")
                
            if 'id="image_link_rule_select"' in content:
                print("✅ 图片链接规则下拉框存在")
            else:
                print("❌ 图片链接规则下拉框不存在")
                
            if 'id="pdf_link_rule_select"' in content:
                print("✅ PDF链接规则下拉框存在")
            else:
                print("❌ PDF链接规则下拉框不存在")
                
            if 'id="news_link_rule_select"' in content:
                print("✅ 新闻链接规则下拉框存在")
            else:
                print("❌ 新闻链接规则下拉框不存在")
                
            if 'loadExtractionRules' in content:
                print("✅ JavaScript函数存在")
            else:
                print("❌ JavaScript函数不存在")
                
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("=" * 50)

if __name__ == "__main__":
    # 执行简单测试
    simple_test()
    
    # 如果需要完整的浏览器测试，取消下面的注释
    # test_frontend_functionality()

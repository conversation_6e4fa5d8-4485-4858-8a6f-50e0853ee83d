#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查Flask应用中注册的所有路由
"""
from app import app

def list_routes():
    """列出所有注册的路由"""
    print("Flask应用中注册的所有路由:")
    print("=" * 60)
    
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': rule.rule
        })
    
    # 按路由规则排序
    routes.sort(key=lambda x: x['rule'])
    
    for route in routes:
        methods = ', '.join([m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']])
        print(f"{route['rule']:<40} {methods:<15} {route['endpoint']}")
    
    print("=" * 60)
    print(f"总共 {len(routes)} 个路由")
    
    # 检查新路由是否存在
    new_routes = ['/api/test/two_links/<int:id>', '/api/test/three_links/<int:id>']
    print("\n检查新路由:")
    for new_route in new_routes:
        found = any(route['rule'] == new_route for route in routes)
        status = "✅ 存在" if found else "❌ 不存在"
        print(f"{new_route:<40} {status}")

if __name__ == "__main__":
    with app.app_context():
        list_routes()

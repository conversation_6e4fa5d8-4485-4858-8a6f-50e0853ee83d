#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试融合输入框功能
"""
import requests
import json

def test_merged_input():
    """测试融合输入框功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🎯 测试融合输入框功能")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查HTML结构变化
            content = response.text
            
            # 检查是否移除了筛选框
            if 'id="page_link_rule_filter"' in content:
                print("   ⚠️ 仍然包含独立的筛选框")
            else:
                print("   ✅ 已移除独立的筛选框")
            
            # 检查新的融合输入框
            fusion_features = [
                ('handlePageLinkRuleInput', '版面链接输入处理'),
                ('showPageLinkSuggestions', '版面链接建议显示'),
                ('page_link_rule_suggestions', '版面链接建议框'),
                ('handleImageLinkRuleInput', '图片链接输入处理'),
                ('handlePdfLinkRuleInput', 'PDF链接输入处理'),
                ('handleNewsLinkRuleInput', '新闻链接输入处理')
            ]
            
            print("   🔍 检查融合功能:")
            for feature, description in fusion_features:
                if feature in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
            # 检查新的placeholder文本
            if '或输入关键词查看智能提示' in content:
                print("   ✅ 新的提示文本正确")
            else:
                print("   ❌ 缺少新的提示文本")
                
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript实现
    print("\n2️⃣ 测试JavaScript实现...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查融合功能的关键特征
            fusion_js_features = [
                ('handlePageLinkRuleInput', '输入处理函数'),
                ('populatePageLinkSuggestions', '建议填充函数'),
                ('selectPageLinkSuggestion', '建议选择函数'),
                ('hideAllSuggestions', '隐藏所有建议函数'),
                ('input.includes(\'//\')', '规则检测逻辑'),
                ('maxSuggestions = 8', '建议数量限制'),
                ('onmouseover', '鼠标悬停效果')
            ]
            
            print("   🎨 检查融合功能特征:")
            for feature_code, description in fusion_js_features:
                if feature_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
            
            # 检查是否移除了旧的筛选相关代码
            old_features = [
                'filterPageLinkRules',
                'togglePageLinkRulesSimple',
                'page_link_rule_filter'
            ]
            
            print("   🗑️ 检查旧功能清理:")
            for feature in old_features:
                if feature in js_content:
                    print(f"   ⚠️ 仍然包含 {feature}")
                else:
                    print(f"   ✅ 已移除 {feature}")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
                
                # 显示一些规则示例，用于测试融合功能
                if data.get('page_link_rules'):
                    print("   📋 可用于测试的规则示例:")
                    for i, rule in enumerate(data['page_link_rules'][:2], 1):
                        rule_preview = rule['rule'][:30] + '...' if len(rule['rule']) > 30 else rule['rule']
                        print(f"   {i}. {rule_preview} ({rule['method']})")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 融合输入框测试完成！")
    
    print("\n📋 融合方案特点:")
    print("✅ 单一输入框 - 既可输入新规则，也可筛选已有规则")
    print("✅ 智能检测 - 自动判断是输入规则还是搜索关键词")
    print("✅ 实时建议 - 输入关键词时显示匹配的规则")
    print("✅ 无缝切换 - 可以在输入和选择之间自由切换")
    print("✅ 简洁界面 - 减少了界面元素，提升用户体验")
    
    print("\n🎯 智能检测逻辑:")
    print("- 包含 '//' 或 '@' 或 '[' 或 '(' → 识别为规则输入，不显示建议")
    print("- 普通文本关键词 → 显示匹配的已有规则建议")
    print("- 空输入 → 隐藏建议框")
    
    print("\n🔄 用户体验:")
    print("1. 输入新规则 → 直接在文本框中输入，不会被打扰")
    print("2. 查找已有规则 → 输入关键词，看到智能建议")
    print("3. 选择建议 → 点击建议项，自动填充规则和方法")
    print("4. 建议数量限制 → 最多显示8个建议，避免列表过长")
    
    print("\n🚀 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 在版面链接规则框中输入关键词（如 'div'）")
    print("3. 观察是否显示智能建议")
    print("4. 点击建议项测试自动填充")
    print("5. 输入完整规则（如 '//div[@class=\"test\"]'）")
    print("6. 观察建议框是否自动隐藏")
    
    print("\n💡 优势总结:")
    print("- 界面更简洁，减少了重复的输入框")
    print("- 用户体验更流畅，无需在两个框之间切换")
    print("- 智能检测避免了不必要的干扰")
    print("- 完全解决了下拉框一闪消失的问题")
    
    return True

if __name__ == "__main__":
    test_merged_input()

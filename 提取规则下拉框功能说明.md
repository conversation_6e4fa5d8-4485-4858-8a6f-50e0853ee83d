# 提取规则下拉框功能说明

## 功能概述

在保持现有功能不变的情况下，为所有提取规则字段添加了下拉框选择功能，用户可以选择已经添加过的提取规则，避免重复输入相同的规则。

## 新增功能

### 1. 智能规则收集
- 系统自动收集所有已添加报纸的提取规则
- 去重处理：相同规则和方法的组合只显示一次
- 显示规则来源：标明规则来自哪个报纸

### 2. 下拉框选择
为以下四个提取规则字段添加了下拉框：
- **版面链接提取规则**
- **版面图片链接提取规则**  
- **版面PDF链接提取规则**
- **版面新闻链接提取规则**

### 3. 自动填充功能
- 选择下拉框中的规则后，自动填充到对应的文本框
- 同时自动设置对应的提取方法（XPath、正则表达式、BeautifulSoup）

## 界面设计

### 布局结构
```
提取规则
├── [下拉框] 选择已有规则...
└── [文本框] 请输入版面链接提取规则
```

### 下拉框选项格式
```
规则内容（前50字符）... (提取方法) - 来源报纸名称
```

示例：
```
//div[@class="swiper-slide"]//a/@href (xpath) - 人民日报
//div[@class="ban_list_nav"]//a/@href (xpath) - 光明日报
<img class="preview" src="(.*?)".*?<a href="(.*?)" (re) - 测试报纸
```

## 技术实现

### 后端API
**接口**: `GET /api/extraction_rules`

**响应格式**:
```json
{
  "success": true,
  "data": {
    "page_link_rules": [
      {
        "rule": "//div[@class=\"swiper-slide\"]//a/@href",
        "method": "xpath",
        "newspaper_name": "人民日报"
      }
    ],
    "image_link_rules": [...],
    "pdf_link_rules": [...],
    "news_link_rules": [...]
  }
}
```

### 前端实现
1. **页面加载时自动获取规则**
   ```javascript
   $(document).ready(function() {
       loadExtractionRules();
   });
   ```

2. **填充下拉框**
   ```javascript
   function populateRuleSelects(rules) {
       // 为每个规则类型填充对应的下拉框
   }
   ```

3. **选择处理函数**
   ```javascript
   function selectPageLinkRule() {
       // 自动填充规则和方法
   }
   ```

## 使用方法

### 方式一：选择已有规则
1. 点击提取规则上方的下拉框
2. 从列表中选择合适的规则
3. 系统自动填充规则内容和提取方法

### 方式二：手动输入（保持原有功能）
1. 直接在文本框中输入新的提取规则
2. 手动选择提取方法
3. 功能与之前完全一致

## 功能特点

### ✅ 优势
1. **提高效率**：避免重复输入相同的规则
2. **减少错误**：选择已验证的规则，降低输入错误
3. **规则复用**：充分利用已有的成功规则
4. **学习参考**：新用户可以参考已有规则的写法

### 🔄 兼容性
1. **完全向下兼容**：原有的手动输入功能保持不变
2. **不影响现有数据**：已有报纸的规则不受影响
3. **可选使用**：用户可以选择使用下拉框或继续手动输入

### 📊 智能去重
- 相同规则内容 + 相同提取方法 = 只显示一次
- 不同报纸使用相同规则时，显示第一个使用该规则的报纸名称

## 实际应用场景

### 场景1：添加同类型报纸
当添加多个结构相似的报纸网站时，可以直接选择已有的规则，大大提高配置效率。

### 场景2：规则学习参考
新用户可以通过查看下拉框中的规则，学习如何编写XPath、正则表达式等提取规则。

### 场景3：规则标准化
团队可以建立标准的规则库，确保不同人员配置的规则保持一致性。

## 测试验证

### API测试
```bash
python test_extraction_rules_api.py
```

### 功能测试
1. 打开报纸添加页面
2. 查看各个提取规则的下拉框是否正常显示
3. 选择规则后检查是否正确填充到文本框
4. 验证提取方法是否自动设置正确

## 注意事项

1. **规则更新**：当添加新报纸或修改现有报纸规则时，下拉框会在下次页面加载时自动更新
2. **规则长度**：下拉框中只显示规则的前50个字符，完整规则会填充到文本框中
3. **方法匹配**：选择规则时会自动设置对应的提取方法，无需手动调整
4. **空规则过滤**：系统会自动过滤空白或无效的规则

## 后续扩展

可以考虑的功能扩展：
1. **规则评分**：根据规则的使用频率和成功率进行排序
2. **规则分类**：按照网站类型或规则复杂度进行分类
3. **规则导入导出**：支持规则的批量导入和导出
4. **规则测试**：在下拉框中直接提供规则测试功能

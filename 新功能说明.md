# 版面链接提取新功能说明

## 功能概述

在原有的版面链接提取测试功能基础上，新增了两个测试按钮，支持从报纸首页同时提取多种类型的链接。

## 新增功能

### 1. 双链接提取测试
- **功能描述**: 从报纸首页同时提取版面链接和媒体链接（图片/PDF）
- **按钮位置**: 版面链接提取规则下方的"测试双链接提取"按钮
- **提取逻辑**: 
  - 使用版面链接提取规则获取所有链接
  - 根据URL后缀自动分类：
    - 版面链接：不以图片或PDF后缀结尾的链接
    - 图片链接：以 .jpg, .jpeg, .png, .gif, .bmp, .webp 结尾的链接
    - PDF链接：以 .pdf 结尾的链接
  - 将图片链接和PDF链接合并为媒体链接

### 2. 三链接提取测试
- **功能描述**: 从报纸首页同时提取版面链接、图片链接和PDF链接
- **按钮位置**: 版面链接提取规则下方的"测试三链接提取"按钮
- **提取逻辑**: 
  - 使用版面链接提取规则获取所有链接
  - 根据URL后缀分别分类为三种类型的链接
  - 分别统计和显示每种类型的链接数量

## 使用方法

### 前提条件
1. 填写报纸URL
2. 配置版面链接提取规则
3. 选择合适的提取方法（XPath、正则表达式或BeautifulSoup）

### 操作步骤
1. 在"报纸添加/编辑"页面填写基本信息
2. 配置版面链接提取规则
3. 点击相应的测试按钮：
   - "测试双链接提取"：测试版面链接和媒体链接的提取
   - "测试三链接提取"：测试版面链接、图片链接、PDF链接的分别提取

### 测试结果显示
- **双链接测试结果**：
  - 版面链接数量
  - 媒体链接数量（包含图片和PDF的总数）
  - 分别显示图片链接和PDF链接的数量
  - 显示前10个版面链接和媒体链接

- **三链接测试结果**：
  - 版面链接数量
  - 图片链接数量
  - PDF链接数量
  - 分别显示前10个各类型链接

## 技术实现

### 后端API
- `/api/test/two_links/0` - 双链接提取测试接口
- `/api/test/three_links/0` - 三链接提取测试接口

### 核心算法
```python
def _classify_links_by_extension(self, links):
    """根据URL后缀分类链接"""
    page_links = []
    image_links = []
    pdf_links = []
    
    for link in links:
        link_lower = link.lower()
        if link_lower.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
            image_links.append(link)
        elif link_lower.endswith('.pdf'):
            pdf_links.append(link)
        else:
            page_links.append(link)
    
    return page_links, image_links, pdf_links
```

## 应用场景

### 场景1：报纸首页包含版面链接和版面图片
- 使用双链接提取测试
- 可以同时获取版面链接和对应的版面图片链接

### 场景2：报纸首页包含版面链接和PDF下载链接
- 使用双链接提取测试
- 可以同时获取版面链接和PDF下载链接

### 场景3：报纸首页同时包含版面链接、版面图片和PDF链接
- 使用三链接提取测试
- 可以分别获取三种类型的链接，便于后续处理

## 注意事项

1. **提取规则要求**: 新功能使用的是版面链接提取规则，因此需要确保该规则能够提取到页面中的所有相关链接
2. **链接分类**: 链接分类完全基于URL后缀，请确保目标网站的链接命名规范
3. **兼容性**: 新功能不影响原有的测试按钮功能，所有原有功能保持不变
4. **性能**: 新功能只进行一次页面抓取和链接提取，然后在内存中进行分类，性能影响很小

## 测试验证

可以使用 `test_system.py` 中的新增测试函数进行功能验证：
- `test_two_links_extraction()` - 测试双链接提取功能
- `test_three_links_extraction()` - 测试三链接提取功能

运行测试：
```bash
python test_system.py
```

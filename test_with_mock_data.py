#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用模拟数据测试新增的API接口
"""
import requests
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

# 模拟HTML页面内容
MOCK_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>测试报纸首页</title>
</head>
<body>
    <div class="page-links">
        <img class="preview" src="page1_thumb.jpg" alt="第一版"><a href="page1.html">第一版</a>
        <img class="preview" src="page2_thumb.png" alt="第二版"><a href="page2.html">第二版</a>
        <img class="preview" src="page3_thumb.gif" alt="第三版"><a href="page3.html">第三版</a>
    </div>
    <div class="pdfs">
        <img class="preview" src="pdf1_thumb.jpg" alt="PDF1"><a href="page1.pdf">第一版PDF</a>
        <img class="preview" src="pdf2_thumb.jpg" alt="PDF2"><a href="page2.pdf">第二版PDF</a>
    </div>
    <div class="mixed">
        <a href="news1.html">新闻1</a>
        <a href="photo.jpeg">照片</a>
        <a href="document.pdf">文档</a>
    </div>
</body>
</html>
"""

class MockHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/test-page':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(MOCK_HTML.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # 禁用日志输出
        pass

def start_mock_server():
    """启动模拟服务器"""
    server = HTTPServer(('localhost', 9999), MockHandler)
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()
    return server

def test_with_mock_data():
    """使用模拟数据测试API"""
    # 启动模拟服务器
    print("启动模拟服务器...")
    server = start_mock_server()
    time.sleep(1)  # 等待服务器启动
    
    base_url = "http://127.0.0.1:5009"
    
    # 测试数据
    test_data = {
        "name": "测试报纸",
        "url": "http://localhost:9999/test-page",
        "page_link_rule": "//a/@href",
        "page_link_method": "xpath"
    }
    
    print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
    print("\n" + "="*60)
    
    # 测试双链接提取
    print("1. 测试双链接提取...")
    try:
        response = requests.post(
            f"{base_url}/api/test/two_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            if data.get('success'):
                print(f"✅ 成功!")
                print(f"   版面链接: {data.get('page_count', 0)} 个")
                print(f"   媒体链接: {data.get('media_count', 0)} 个")
                print(f"   - 图片链接: {data.get('image_count', 0)} 个")
                print(f"   - PDF链接: {data.get('pdf_count', 0)} 个")
                print(f"   总链接数: {data.get('total_count', 0)} 个")
                
                if data.get('page_links'):
                    print(f"   版面链接示例: {data['page_links'][:3]}")
                if data.get('media_links'):
                    print(f"   媒体链接示例: {data['media_links'][:3]}")
            else:
                print(f"❌ 失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print("响应内容:", response.text[:200])
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "="*60)
    
    # 测试三链接提取
    print("2. 测试三链接提取...")
    try:
        response = requests.post(
            f"{base_url}/api/test/three_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            if data.get('success'):
                print(f"✅ 成功!")
                print(f"   版面链接: {data.get('page_count', 0)} 个")
                print(f"   图片链接: {data.get('image_count', 0)} 个")
                print(f"   PDF链接: {data.get('pdf_count', 0)} 个")
                print(f"   总链接数: {data.get('total_count', 0)} 个")
                
                if data.get('page_links'):
                    print(f"   版面链接: {data['page_links']}")
                if data.get('image_links'):
                    print(f"   图片链接: {data['image_links']}")
                if data.get('pdf_links'):
                    print(f"   PDF链接: {data['pdf_links']}")
            else:
                print(f"❌ 失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print("响应内容:", response.text[:200])
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "="*60)

    # 测试多捕获组正则表达式
    print("3. 测试多捕获组正则表达式...")
    regex_test_data = {
        "name": "测试报纸",
        "url": "http://localhost:9999/test-page",
        "page_link_rule": "<img class=\"preview\" src=\"(.*?)\".*?<a href=\"(.*?)\"",
        "page_link_method": "re"
    }

    try:
        response = requests.post(
            f"{base_url}/api/test/three_links/0",
            json=regex_test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            if data.get('success'):
                print(f"✅ 成功!")
                print(f"   版面链接: {data.get('page_count', 0)} 个")
                print(f"   图片链接: {data.get('image_count', 0)} 个")
                print(f"   PDF链接: {data.get('pdf_count', 0)} 个")
                print(f"   总链接数: {data.get('total_count', 0)} 个")

                if data.get('page_links'):
                    print(f"   版面链接: {data['page_links']}")
                if data.get('image_links'):
                    print(f"   图片链接: {data['image_links']}")
                if data.get('pdf_links'):
                    print(f"   PDF链接: {data['pdf_links']}")
            else:
                print(f"❌ 失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print("响应内容:", response.text[:200])
    except Exception as e:
        print(f"❌ 请求失败: {e}")

    print("\n" + "="*60)
    print("测试完成!")

if __name__ == "__main__":
    test_with_mock_data()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试图片链接分类功能
"""
import requests
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

# 模拟HTML页面内容，包含各种格式的图片链接
MOCK_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>测试报纸首页</title>
</head>
<body>
    <div class="page-links">
        <a href="page1.html">第一版</a>
        <a href="page2.html">第二版</a>
        <a href="page3.html">第三版</a>
    </div>
    <div class="images">
        <!-- 标准图片链接 -->
        <a href="page1_thumb.jpg">第一版缩略图</a>
        <a href="page2_thumb.png">第二版缩略图</a>
        <a href="page3_thumb.gif">第三版缩略图</a>
        
        <!-- 带版本号的图片链接 -->
        <a href="7ad4507409.jpg.1">特殊图片1</a>
        <a href="image123.png.2">特殊图片2</a>
        <a href="photo.jpeg.v3">特殊图片3</a>
        
        <!-- 路径中包含图片格式的链接 -->
        <a href="/images/jpg/thumbnail.html">包含jpg路径的页面</a>
        <a href="/gallery/png/view.php">包含png路径的页面</a>
        
        <!-- 查询参数中包含图片格式的链接 -->
        <a href="/view?file=image.jpg&size=large">查询参数图片</a>
        <a href="/download?type=png&id=123">查询参数PNG</a>
    </div>
    <div class="pdfs">
        <!-- 标准PDF链接 -->
        <a href="page1.pdf">第一版PDF</a>
        <a href="page2.pdf">第二版PDF</a>
        
        <!-- 带版本号的PDF链接 -->
        <a href="document.pdf.v1">特殊PDF</a>
        
        <!-- 查询参数中包含PDF的链接 -->
        <a href="/download?file=report.pdf&format=A4">查询参数PDF</a>
    </div>
    <div class="mixed">
        <a href="news1.html">新闻1</a>
        <a href="article.php">文章</a>
    </div>
</body>
</html>
"""

class MockHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/test-page':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(MOCK_HTML.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # 禁用日志输出
        pass

def start_mock_server():
    """启动模拟服务器"""
    server = HTTPServer(('localhost', 9998), MockHandler)
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()
    return server

def test_image_classification():
    """测试图片链接分类功能"""
    # 启动模拟服务器
    print("启动模拟服务器...")
    server = start_mock_server()
    time.sleep(1)  # 等待服务器启动
    
    base_url = "http://127.0.0.1:5009"
    
    # 测试数据
    test_data = {
        "name": "测试报纸",
        "url": "http://localhost:9998/test-page",
        "page_link_rule": "//a/@href",
        "page_link_method": "xpath"
    }
    
    print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
    print("\n" + "="*70)
    
    # 测试三链接提取
    print("测试图片链接分类...")
    try:
        response = requests.post(
            f"{base_url}/api/test/three_links/0",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            if data.get('success'):
                print(f"✅ 成功!")
                print(f"   版面链接: {data.get('page_count', 0)} 个")
                print(f"   图片链接: {data.get('image_count', 0)} 个")
                print(f"   PDF链接: {data.get('pdf_count', 0)} 个")
                print(f"   总链接数: {data.get('total_count', 0)} 个")
                
                print("\n📄 版面链接:")
                for i, link in enumerate(data.get('page_links', []), 1):
                    print(f"   {i}. {link}")
                
                print("\n🖼️ 图片链接:")
                for i, link in enumerate(data.get('image_links', []), 1):
                    print(f"   {i}. {link}")
                
                print("\n📋 PDF链接:")
                for i, link in enumerate(data.get('pdf_links', []), 1):
                    print(f"   {i}. {link}")
                
                # 验证特殊格式的图片链接是否被正确识别
                image_links = data.get('image_links', [])
                special_cases = [
                    '7ad4507409.jpg.1',
                    'image123.png.2', 
                    'photo.jpeg.v3',
                    'image.jpg&size=large',
                    'type=png&id=123'
                ]
                
                print("\n🔍 特殊格式验证:")
                for case in special_cases:
                    found = any(case in link for link in image_links)
                    status = "✅ 正确识别" if found else "❌ 未识别"
                    print(f"   {case}: {status}")
                    
            else:
                print(f"❌ 失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print("响应内容:", response.text[:200])
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "="*70)
    print("测试完成!")

if __name__ == "__main__":
    test_image_classification()

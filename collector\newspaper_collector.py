# -*- coding: utf-8 -*-
"""
报纸采集器
"""
import requests
import re
import json
import time
from datetime import datetime
from urllib.parse import urljoin, urlparse
from lxml import html, etree
from bs4 import BeautifulSoup

class NewspaperCollector:
    """报纸采集器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.timeout = 30
        
    def _replace_date_placeholders(self, url):
        """替换URL中的日期占位符"""
        now = datetime.now()
        url = url.replace('YYYY', now.strftime('%Y'))
        url = url.replace('MM', now.strftime('%m'))
        url = url.replace('DD', now.strftime('%d'))
        return url
    
    def _get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            return response.text
        except Exception as e:
            raise Exception(f"获取页面内容失败: {str(e)}")
    
    def _extract_links_by_regex(self, content, pattern):
        """使用正则表达式提取链接"""
        try:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            return matches if isinstance(matches, list) else [matches] if matches else []
        except Exception as e:
            raise Exception(f"正则表达式提取失败: {str(e)}")
    
    def _extract_links_by_xpath(self, content, xpath):
        """使用XPath提取链接"""
        try:
            tree = html.fromstring(content)
            results = tree.xpath(xpath)
            return [str(result) for result in results]
        except Exception as e:
            raise Exception(f"XPath提取失败: {str(e)}")
    
    def _extract_links_by_bs4(self, content, selector):
        """使用BeautifulSoup提取链接"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            # 这里需要根据selector的格式来解析
            # 简化处理，假设selector是CSS选择器
            elements = soup.select(selector)
            results = []
            for element in elements:
                if element.get('href'):
                    results.append(element.get('href'))
                elif element.get('src'):
                    results.append(element.get('src'))
                else:
                    results.append(element.get_text().strip())
            return results
        except Exception as e:
            raise Exception(f"BeautifulSoup提取失败: {str(e)}")
    
    def _extract_links(self, content, rule, method, base_url=None):
        """根据方法提取链接"""
        if not rule:
            return []
        
        if method == 're':
            links = self._extract_links_by_regex(content, rule)
        elif method == 'xpath':
            links = self._extract_links_by_xpath(content, rule)
        elif method == 'bs4':
            links = self._extract_links_by_bs4(content, rule)
        else:
            raise Exception(f"不支持的提取方法: {method}")
        
        # 处理相对链接
        if base_url:
            absolute_links = []
            for link in links:
                if link.startswith('http'):
                    absolute_links.append(link)
                else:
                    absolute_links.append(urljoin(base_url, link))
            return absolute_links
        
        return links
    
    def test_page_links(self, newspaper):
        """测试版面链接提取"""
        try:
            start_time = time.time()
            
            # 替换日期占位符
            url = self._replace_date_placeholders(newspaper.url)
            
            # 获取页面内容
            content = self._get_page_content(url)
            
            # 提取版面链接
            page_links = self._extract_links(
                content, 
                newspaper.page_link_rule, 
                newspaper.page_link_method,
                url
            )
            
            duration = int(time.time() - start_time)
            
            return {
                'success': True,
                'url': url,
                'page_links': page_links[:10],  # 只返回前10个链接
                'total_count': len(page_links),
                'duration': duration,
                'message': f'成功提取到{len(page_links)}个版面链接'
            }
        except Exception as e:
            return {
                'success': False,
                'message': str(e),
                'duration': int(time.time() - start_time) if 'start_time' in locals() else 0
            }
    
    def test_page_content(self, newspaper, page_url):
        """测试版面内容提取"""
        try:
            start_time = time.time()
            
            # 获取版面页面内容
            content = self._get_page_content(page_url)
            
            # 提取图片链接
            image_links = self._extract_links(
                content,
                newspaper.image_link_rule,
                newspaper.image_link_method,
                page_url
            )
            
            # 提取PDF链接
            pdf_links = self._extract_links(
                content,
                newspaper.pdf_link_rule,
                newspaper.pdf_link_method,
                page_url
            )
            
            # 提取新闻链接
            news_links = self._extract_links(
                content,
                newspaper.news_link_rule,
                newspaper.news_link_method,
                page_url
            )
            
            duration = int(time.time() - start_time)
            
            return {
                'success': True,
                'page_url': page_url,
                'image_links': image_links,
                'pdf_links': pdf_links,
                'news_links': news_links,
                'duration': duration,
                'message': f'成功提取图片{len(image_links)}个，PDF{len(pdf_links)}个，新闻{len(news_links)}个'
            }
        except Exception as e:
            return {
                'success': False,
                'message': str(e),
                'duration': int(time.time() - start_time) if 'start_time' in locals() else 0
            }
    
    def test_full_collection(self, newspaper):
        """测试完整采集流程"""
        try:
            start_time = time.time()
            
            # 第一步：获取版面链接
            page_links_result = self.test_page_links(newspaper)
            if not page_links_result['success']:
                return page_links_result
            
            page_links = page_links_result.get('page_links', [])
            if not page_links:
                return {
                    'success': False,
                    'message': '未能获取到版面链接',
                    'duration': int(time.time() - start_time)
                }
            
            # 第二步：采集前3个版面的内容（测试用）
            results = []
            for i, page_link in enumerate(page_links[:3]):
                page_result = self.test_page_content(newspaper, page_link)
                if page_result['success']:
                    results.append({
                        'page_link': page_link,
                        'image_links': page_result.get('image_links', []),
                        'pdf_links': page_result.get('pdf_links', []),
                        'news_links': page_result.get('news_links', [])
                    })
                else:
                    results.append({
                        'page_link': page_link,
                        'error': page_result['message']
                    })
            
            duration = int(time.time() - start_time)
            
            return {
                'success': True,
                'total_pages': len(page_links),
                'tested_pages': len(results),
                'results': results,
                'duration': duration,
                'message': f'测试完成，共{len(page_links)}个版面，测试了前{len(results)}个'
            }
        except Exception as e:
            return {
                'success': False,
                'message': str(e),
                'duration': int(time.time() - start_time) if 'start_time' in locals() else 0
            }

    def collect_newspaper(self, newspaper):
        """完整采集报纸"""
        try:
            start_time = time.time()

            # 第一步：获取版面链接
            url = self._replace_date_placeholders(newspaper.url)
            content = self._get_page_content(url)

            page_links = self._extract_links(
                content,
                newspaper.page_link_rule,
                newspaper.page_link_method,
                url
            )

            if not page_links:
                return {
                    'success': False,
                    'message': '未能获取到版面链接',
                    'duration': int(time.time() - start_time)
                }

            # 第二步：采集每个版面的内容
            results = []
            for page_link in page_links:
                try:
                    page_content = self._get_page_content(page_link)

                    # 提取版面标题
                    page_title = self._extract_page_title(page_content)

                    # 提取图片链接
                    image_links = self._extract_links(
                        page_content,
                        newspaper.image_link_rule,
                        newspaper.image_link_method,
                        page_link
                    )

                    # 提取PDF链接
                    pdf_links = self._extract_links(
                        page_content,
                        newspaper.pdf_link_rule,
                        newspaper.pdf_link_method,
                        page_link
                    )

                    # 提取新闻链接
                    news_links = self._extract_links(
                        page_content,
                        newspaper.news_link_rule,
                        newspaper.news_link_method,
                        page_link
                    )

                    results.append({
                        'page_link': page_link,
                        'page_title': page_title,
                        'image_link': image_links[0] if image_links else None,
                        'pdf_link': pdf_links[0] if pdf_links else None,
                        'news_links': news_links
                    })

                    # 添加延迟避免过于频繁的请求
                    time.sleep(1)

                except Exception as e:
                    # 单个版面采集失败不影响整体
                    results.append({
                        'page_link': page_link,
                        'page_title': None,
                        'image_link': None,
                        'pdf_link': None,
                        'news_links': [],
                        'error': str(e)
                    })
                    continue

            duration = int(time.time() - start_time)

            return {
                'success': True,
                'data': results,
                'duration': duration,
                'message': f'采集完成，共采集{len(results)}个版面'
            }
        except Exception as e:
            return {
                'success': False,
                'message': str(e),
                'duration': int(time.time() - start_time) if 'start_time' in locals() else 0
            }

    def _extract_page_title(self, content):
        """提取版面标题"""
        try:
            # 尝试从title标签提取
            soup = BeautifulSoup(content, 'html.parser')
            title_tag = soup.find('title')
            if title_tag:
                return title_tag.get_text().strip()

            # 尝试从h1标签提取
            h1_tag = soup.find('h1')
            if h1_tag:
                return h1_tag.get_text().strip()

            return None
        except:
            return None

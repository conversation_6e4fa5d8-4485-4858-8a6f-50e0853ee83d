# -*- coding: utf-8 -*-
"""
系统功能测试脚本
"""
import requests
import json
import time
from datetime import datetime

class SystemTester:
    """系统测试类"""
    
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
    
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/statistics/dashboard")
            if response.status_code == 200:
                self.log_test("API连接测试", True, "API服务正常")
                return True
            else:
                self.log_test("API连接测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API连接测试", False, f"连接失败: {str(e)}")
            return False
    
    def test_create_newspaper(self):
        """测试创建报纸"""
        test_data = {
            'name': '测试报纸_' + str(int(time.time())),
            'url': 'http://example.com/YYYY-MM/DD/test.html',
            'province': '测试省',
            'city': '测试市',
            'page_link_rule': '//a[@class="page-link"]/@href',
            'page_link_method': 'xpath',
            'image_link_rule': '//img[@class="page-image"]/@src',
            'image_link_method': 'xpath',
            'pdf_link_rule': '//a[contains(@href, ".pdf")]/@href',
            'pdf_link_method': 'xpath',
            'news_link_rule': '//a[@class="news-link"]/@href',
            'news_link_method': 'xpath'
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/newspapers",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.test_newspaper_id = result['data']['id']
                    self.log_test("创建报纸测试", True, f"报纸ID: {self.test_newspaper_id}")
                    return True
                else:
                    self.log_test("创建报纸测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("创建报纸测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("创建报纸测试", False, f"请求失败: {str(e)}")
            return False
    
    def test_get_newspapers(self):
        """测试获取报纸列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/newspapers")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    count = len(result.get('data', []))
                    self.log_test("获取报纸列表测试", True, f"获取到 {count} 份报纸")
                    return True
                else:
                    self.log_test("获取报纸列表测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("获取报纸列表测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("获取报纸列表测试", False, f"请求失败: {str(e)}")
            return False
    
    def test_update_newspaper(self):
        """测试更新报纸"""
        if not hasattr(self, 'test_newspaper_id'):
            self.log_test("更新报纸测试", False, "没有可用的测试报纸ID")
            return False
        
        update_data = {
            'name': '更新后的测试报纸_' + str(int(time.time())),
            'province': '更新省',
            'city': '更新市'
        }
        
        try:
            response = self.session.put(
                f"{self.base_url}/api/newspapers/{self.test_newspaper_id}",
                json=update_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_test("更新报纸测试", True, "报纸更新成功")
                    return True
                else:
                    self.log_test("更新报纸测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("更新报纸测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("更新报纸测试", False, f"请求失败: {str(e)}")
            return False
    
    def test_page_links_extraction(self):
        """测试版面链接提取"""
        test_data = {
            'name': '测试报纸',
            'url': 'http://httpbin.org/html',  # 使用httpbin作为测试URL
            'page_link_rule': '//a/@href',
            'page_link_method': 'xpath'
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/test/page_links/0",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('data', {})
                    count = data.get('total_count', 0)
                    self.log_test("版面链接提取测试", True, f"提取到 {count} 个链接")
                    return True
                else:
                    self.log_test("版面链接提取测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("版面链接提取测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("版面链接提取测试", False, f"请求失败: {str(e)}")
            return False
    
    def test_collection_results(self):
        """测试采集结果查询"""
        try:
            response = self.session.get(f"{self.base_url}/api/collection/results")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    count = len(result.get('data', []))
                    self.log_test("采集结果查询测试", True, f"查询到 {count} 条结果")
                    return True
                else:
                    self.log_test("采集结果查询测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("采集结果查询测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("采集结果查询测试", False, f"请求失败: {str(e)}")
            return False
    
    def test_dashboard_statistics(self):
        """测试首页统计"""
        try:
            response = self.session.get(f"{self.base_url}/api/statistics/dashboard")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('data', {})
                    total = data.get('total_newspapers', 0)
                    self.log_test("首页统计测试", True, f"统计数据正常，总报纸数: {total}")
                    return True
                else:
                    self.log_test("首页统计测试", False, result.get('message', '未知错误'))
                    return False
            else:
                self.log_test("首页统计测试", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("首页统计测试", False, f"请求失败: {str(e)}")
            return False
    
    def cleanup_test_data(self):
        """清理测试数据"""
        if hasattr(self, 'test_newspaper_id'):
            try:
                response = self.session.delete(f"{self.base_url}/api/newspapers/{self.test_newspaper_id}")
                if response.status_code == 200:
                    self.log_test("清理测试数据", True, "测试报纸已删除")
                else:
                    self.log_test("清理测试数据", False, f"删除失败，状态码: {response.status_code}")
            except Exception as e:
                self.log_test("清理测试数据", False, f"删除失败: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("开始系统功能测试")
        print("=" * 60)
        
        # 基础连接测试
        if not self.test_api_connection():
            print("API连接失败，停止测试")
            return
        
        # 运行各项测试
        tests = [
            self.test_dashboard_statistics,
            self.test_get_newspapers,
            self.test_create_newspaper,
            self.test_update_newspaper,
            self.test_page_links_extraction,
            self.test_collection_results,
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # 避免请求过于频繁
        
        # 清理测试数据
        self.cleanup_test_data()
        
        # 输出测试结果摘要
        self.print_summary()
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print("=" * 60)

def main():
    """主函数"""
    tester = SystemTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()

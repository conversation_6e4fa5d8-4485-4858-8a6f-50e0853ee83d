#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试最终修复效果
"""
import requests
import json

def test_final_fix():
    """测试最终修复效果"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔧 测试最终修复效果")
    print("=" * 60)
    
    # 1. 测试页面访问
    print("1️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    # 2. 测试JavaScript修复
    print("\n2️⃣ 测试JavaScript修复...")
    try:
        response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
        if response.status_code == 200:
            js_content = response.text
            
            # 检查关键修复点
            fixes = [
                ('window.dropdownStates', '全局状态跟踪'),
                ('dropdownStates.pageLink', '版面链接状态'),
                ('bootstrap.Dropdown.getInstance', 'Bootstrap实例管理'),
                ('setTimeout(() => {', '延迟执行'),
                ('const isVisible = window.dropdownStates', '状态检测优化')
            ]
            
            print("   🔧 检查关键修复点:")
            for fix_code, description in fixes:
                if fix_code in js_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} 缺失")
                    
        else:
            print(f"   ❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ JavaScript文件请求失败: {e}")
        return False
    
    # 3. 测试API正常性
    print("\n3️⃣ 测试API正常性...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"   ✅ API正常 - 获取到规则数据")
                print(f"   📄 版面链接规则: {len(data.get('page_link_rules', []))} 个")
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 最终修复测试完成！")
    
    print("\n📋 修复方案总结:")
    print("✅ 全局状态跟踪 - 使用window.dropdownStates跟踪状态")
    print("✅ 实例管理优化 - 正确获取和管理Bootstrap实例")
    print("✅ 状态检测改进 - 避免DOM状态检测的时机问题")
    print("✅ 延迟执行保持 - 继续使用setTimeout避免事件冲突")
    
    print("\n🔄 预期行为:")
    print("1. 第一次点击输入框 → 下拉框正常显示（不会一闪消失）")
    print("2. 再次点击输入框 → 下拉框正常隐藏")
    print("3. 第三次点击输入框 → 下拉框正常显示")
    print("4. 循环往复，稳定切换")
    
    print("\n💡 技术改进:")
    print("- 使用全局状态变量而不是DOM状态检测")
    print("- 在显示/隐藏函数中同步更新状态")
    print("- 避免了Bootstrap实例初始化的时机问题")
    print("- 保持了原有的延迟执行机制")
    
    print("\n🚀 测试建议:")
    print("1. 在浏览器中打开 http://127.0.0.1:5009/add_newspaper")
    print("2. 点击版面链接筛选框，观察是否正常显示")
    print("3. 再次点击同一筛选框，观察是否正常隐藏")
    print("4. 重复测试多次，验证稳定性")
    print("5. 测试其他筛选框的切换功能")
    print("6. 测试筛选和选择功能是否正常")
    
    return True

if __name__ == "__main__":
    test_final_fix()

{% extends "base.html" %}

{% block title %}采集结果 - 报纸采集平台{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">采集结果</li>
    </ol>
</nav>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-database me-2"></i>采集结果
    </h1>
    <div>
        <button class="btn btn-outline-primary" onclick="exportResults()">
            <i class="fas fa-download me-1"></i>导出结果
        </button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 筛选条件 -->
<div class="card mb-4">
    <div class="card-body">
        <form id="filter-form" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">报纸名称</label>
                <input type="text" class="form-control" id="filter-newspaper-name" placeholder="输入报纸名称">
            </div>
            <div class="col-md-2">
                <label class="form-label">省份</label>
                <select class="form-select" id="filter-province">
                    <option value="">全部省份</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">城市</label>
                <select class="form-select" id="filter-city">
                    <option value="">全部城市</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">采集日期</label>
                <input type="date" class="form-control" id="filter-collection-date">
            </div>
            <div class="col-md-2">
                <label class="form-label">发布状态</label>
                <select class="form-select" id="filter-publish-status">
                    <option value="">全部状态</option>
                    <option value="已发布">已发布</option>
                    <option value="未发布">未发布</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="total-results">-</h4>
                        <p class="card-text">总采集结果</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="published-results">-</h4>
                        <p class="card-text">已发布</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="unpublished-results">-</h4>
                        <p class="card-text">未发布</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="today-results">-</h4>
                        <p class="card-text">今日采集</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button class="btn btn-success" onclick="batchPublish()">
                    <i class="fas fa-upload me-1"></i>批量发布
                </button>
                <button class="btn btn-secondary" onclick="batchUnpublish()">
                    <i class="fas fa-download me-1"></i>取消发布
                </button>
                <button class="btn btn-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
            </div>
            <div>
                <span class="text-muted">已选择 <span id="selected-count">0</span> 项</span>
            </div>
        </div>
    </div>
</div>

<!-- 采集结果列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>报纸信息</th>
                        <th>版面信息</th>
                        <th>采集内容</th>
                        <th>采集时间</th>
                        <th>发布状态</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="results-table">
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav id="pagination-container" class="mt-3"></nav>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">采集结果详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detail-content">
                <!-- 详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 发布状态更新模态框 -->
<div class="modal fade" id="publishModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新发布状态</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要更新选中项目的发布状态吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmPublishUpdate()">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/collection_results.js') }}"></script>
{% endblock %}

# 规则筛选功能说明

## 功能概述

在原有的提取规则下拉框基础上，新增了**输入筛选功能**，用户可以通过输入关键词来快速筛选和查找匹配的提取规则，大大提高了规则选择的效率。

## 新增功能特点

### 🔍 智能筛选
- **实时筛选**：输入时立即显示匹配结果
- **多字段匹配**：支持按规则内容、提取方法、来源报纸筛选
- **模糊匹配**：不区分大小写的包含匹配
- **即时反馈**：未找到匹配规则时显示提示信息

### 🎨 界面优化
- **美观设计**：使用Bootstrap下拉框组件
- **清晰布局**：规则内容和元信息分行显示
- **滚动支持**：下拉框支持滚动，最大高度200px
- **响应式**：下拉框宽度自适应输入框

### ⚡ 性能优化
- **索引访问**：通过索引快速定位规则数据
- **完整传递**：规则内容完整保存，避免截断
- **内存缓存**：规则数据缓存在全局变量中

## 界面设计

### 筛选框布局
```
┌─────────────────────────────────────────┐
│ 输入关键词筛选已有规则...                │
├─────────────────────────────────────────┤
│ ▼ //div[@class="swiper-slide"]//a/@href │
│   xpath - 人民日报                      │
├─────────────────────────────────────────┤
│ ▼ //div[@class="ban_list_nav"]//a/@href │
│   xpath - 光明日报                      │
├─────────────────────────────────────────┤
│ ▼ //div[@class="bjdh_list"]//a/@href    │
│   xpath - 经济日报                      │
└─────────────────────────────────────────┘
```

### 筛选效果示例
**输入 "swiper"**：
```
┌─────────────────────────────────────────┐
│ swiper                                  │
├─────────────────────────────────────────┤
│ ▼ //div[@class="swiper-slide"]//a/@href │
│   xpath - 人民日报                      │
└─────────────────────────────────────────┘
```

**输入 "xpath"**：
```
┌─────────────────────────────────────────┐
│ xpath                                   │
├─────────────────────────────────────────┤
│ ▼ //div[@class="ban_list_nav"]//a/@href │
│   xpath - 光明日报                      │
├─────────────────────────────────────────┤
│ ▼ //div[@class="bjdh_list"]//a/@href    │
│   xpath - 经济日报                      │
└─────────────────────────────────────────┘
```

## 使用方法

### 方式一：直接筛选
1. **点击筛选框**：点击"输入关键词筛选已有规则..."输入框
2. **输入关键词**：输入要筛选的关键词（如：xpath、div、人民日报等）
3. **查看结果**：系统实时显示匹配的规则
4. **选择规则**：点击匹配的规则项

### 方式二：浏览所有规则
1. **点击筛选框**：点击输入框但不输入内容
2. **查看所有规则**：系统显示所有可用规则
3. **选择规则**：直接点击需要的规则

### 方式三：组合筛选
1. **输入部分关键词**：如输入"div"
2. **查看筛选结果**：查看包含"div"的所有规则
3. **继续细化**：继续输入更多关键词进一步筛选

## 筛选规则

### 匹配字段
筛选功能会在以下字段中查找匹配：
1. **规则内容**：XPath、正则表达式、CSS选择器等
2. **提取方法**：xpath、re、bs4
3. **来源报纸**：报纸名称

### 匹配方式
- **包含匹配**：只要字段中包含关键词即匹配
- **不区分大小写**：输入"XPATH"和"xpath"效果相同
- **多关键词**：输入多个词时，需要同时匹配所有词

### 筛选示例

| 输入关键词 | 匹配规则示例 | 说明 |
|------------|--------------|------|
| `xpath` | 所有使用XPath方法的规则 | 按提取方法筛选 |
| `div` | 包含div标签的规则 | 按规则内容筛选 |
| `人民日报` | 来自人民日报的所有规则 | 按来源报纸筛选 |
| `swiper` | 包含swiper类的规则 | 按CSS类名筛选 |
| `@href` | 提取href属性的规则 | 按属性筛选 |
| `contains` | 使用contains函数的规则 | 按XPath函数筛选 |

## 技术实现

### 前端实现
```javascript
// 筛选函数
function filterPageLinkRules() {
    const filter = $('#page_link_rule_filter').val();
    populatePageLinkRules(filter);
    showPageLinkRules();
}

// 填充筛选结果
function populatePageLinkRules(filter = '') {
    const rules = window.extractionRulesData.page_link_rules;
    const filteredRules = filter ? rules.filter(rule => 
        rule.rule.toLowerCase().includes(filter.toLowerCase()) ||
        rule.method.toLowerCase().includes(filter.toLowerCase()) ||
        rule.newspaper_name.toLowerCase().includes(filter.toLowerCase())
    ) : rules;
    // ... 渲染逻辑
}
```

### HTML结构
```html
<div class="dropdown">
    <input type="text" class="form-control" 
           id="page_link_rule_filter" 
           placeholder="输入关键词筛选已有规则..." 
           oninput="filterPageLinkRules()" 
           onfocus="showPageLinkRules()">
    <ul class="dropdown-menu w-100" 
        id="page_link_rule_dropdown" 
        style="max-height: 200px; overflow-y: auto;">
        <!-- 动态生成的规则列表 -->
    </ul>
</div>
```

## 优势对比

### 修改前（普通下拉框）
- ❌ 需要逐个查看所有规则
- ❌ 规则多时查找困难
- ❌ 无法快速定位目标规则
- ❌ 用户体验较差

### 修改后（筛选下拉框）
- ✅ 输入关键词快速筛选
- ✅ 支持多字段匹配
- ✅ 实时显示筛选结果
- ✅ 大大提高选择效率

## 应用场景

### 场景1：按技术类型筛选
- 输入"xpath"查找所有XPath规则
- 输入"re"查找所有正则表达式规则
- 输入"bs4"查找所有BeautifulSoup规则

### 场景2：按网站结构筛选
- 输入"div"查找基于div标签的规则
- 输入"class"查找基于CSS类的规则
- 输入"@href"查找提取链接的规则

### 场景3：按报纸来源筛选
- 输入"人民日报"查找人民日报的规则
- 输入"光明"查找光明日报的规则
- 输入"经济"查找经济日报的规则

### 场景4：按功能特征筛选
- 输入"swiper"查找轮播图相关规则
- 输入"nav"查找导航相关规则
- 输入"list"查找列表相关规则

## 注意事项

1. **筛选框清空**：选择规则后筛选框会自动清空
2. **下拉框隐藏**：选择规则后下拉框会自动隐藏
3. **无匹配提示**：未找到匹配规则时会显示提示信息
4. **性能考虑**：筛选在前端进行，响应速度快

## 后续扩展

可以考虑的功能扩展：
1. **高级筛选**：支持正则表达式筛选
2. **标签分类**：为规则添加标签进行分类筛选
3. **收藏功能**：收藏常用规则，优先显示
4. **使用统计**：根据使用频率排序规则

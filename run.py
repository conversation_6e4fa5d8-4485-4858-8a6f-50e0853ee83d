# -*- coding: utf-8 -*-
"""
应用启动脚本
"""
import os
import sys
from app import app
from database import db
from models import Newspaper, CollectionRule, CollectionResult, CollectionTask

def create_tables():
    """创建数据库表"""
    with app.app_context():
        try:
            db.create_all()
            print("数据库表创建成功！")
        except Exception as e:
            print(f"创建数据库表失败: {e}")

def create_sample_data():
    """创建示例数据"""
    with app.app_context():
        try:
            # 检查是否已有数据
            if Newspaper.query.count() > 0:
                print("数据库中已有数据，跳过示例数据创建")
                return
            
            # 创建示例报纸
            sample_newspapers = [
                {
                    'name': '人民日报',
                    'url': 'http://paper.people.com.cn/rmrb/html/YYYY-MM/DD/nbs.D110000renmrb_01.htm',
                    'province': '北京',
                    'city': '北京',
                    'page_link_rule': '//div[@class="swiper-slide"]//a/@href',
                    'page_link_method': 'xpath',
                    'image_link_rule': '//div[@class="pic"]//img/@src',
                    'image_link_method': 'xpath',
                    'pdf_link_rule': '//a[contains(@href, ".pdf")]/@href',
                    'pdf_link_method': 'xpath',
                    'news_link_rule': '//div[@class="news"]//a/@href',
                    'news_link_method': 'xpath'
                },
                {
                    'name': '光明日报',
                    'url': 'http://epaper.gmw.cn/gmrb/html/YYYY-MM/DD/nbs.D110000gmrb_01.htm',
                    'province': '北京',
                    'city': '北京',
                    'page_link_rule': '//div[@class="ban_list_nav"]//a/@href',
                    'page_link_method': 'xpath',
                    'image_link_rule': '//div[@class="pic"]//img/@src',
                    'image_link_method': 'xpath',
                    'pdf_link_rule': '//a[contains(text(), "PDF")]/@href',
                    'pdf_link_method': 'xpath',
                    'news_link_rule': '//div[@class="news_list"]//a/@href',
                    'news_link_method': 'xpath'
                },
                {
                    'name': '经济日报',
                    'url': 'http://paper.ce.cn/jjrb/html/YYYY-MM/DD/nbs.D110000jjrb_01.htm',
                    'province': '北京',
                    'city': '北京',
                    'page_link_rule': '//div[@class="bjdh_list"]//a/@href',
                    'page_link_method': 'xpath',
                    'image_link_rule': '//div[@class="image"]//img/@src',
                    'image_link_method': 'xpath',
                    'pdf_link_rule': '//a[contains(@href, "pdf")]/@href',
                    'pdf_link_method': 'xpath',
                    'news_link_rule': '//div[@class="news_title"]//a/@href',
                    'news_link_method': 'xpath'
                }
            ]
            
            for newspaper_data in sample_newspapers:
                newspaper = Newspaper(**newspaper_data)
                db.session.add(newspaper)
            
            db.session.commit()
            print("示例数据创建成功！")
            
        except Exception as e:
            db.session.rollback()
            print(f"创建示例数据失败: {e}")

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_cors',
        'pymysql',
        'requests',
        'lxml',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("报纸采集平台启动脚本")
    print("=" * 50)
    
    # 检查依赖
    # if not check_dependencies():
    #     sys.exit(1)
    
    # 创建数据库表
    create_tables()
    
    # 创建示例数据
    create_sample_data()
    
    print("\n" + "=" * 50)
    print("系统启动成功！")
    print("访问地址: http://localhost:5009")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 启动应用
    try:
        app.run(debug=True, host='0.0.0.0', port=5009)
    except KeyboardInterrupt:
        print("\n服务已停止")

if __name__ == '__main__':
    main()

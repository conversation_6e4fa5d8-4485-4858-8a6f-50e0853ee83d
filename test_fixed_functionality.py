#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""
import requests
import json

def test_fixed_functionality():
    """测试修复后的功能"""
    base_url = "http://127.0.0.1:5009"
    
    print("🔧 测试修复后的功能")
    print("=" * 60)
    
    # 1. 测试排重后的提取规则API
    print("1️⃣ 测试排重后的提取规则API...")
    try:
        response = requests.get(f"{base_url}/api/extraction_rules", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                
                print(f"   ✅ API正常")
                print(f"   📄 版面链接规则数量: {len(data.get('page_link_rules', []))}")
                print(f"   🖼️ 图片链接规则数量: {len(data.get('image_link_rules', []))}")
                print(f"   📋 PDF链接规则数量: {len(data.get('pdf_link_rules', []))}")
                print(f"   📰 新闻链接规则数量: {len(data.get('news_link_rules', []))}")
                
                # 检查是否有重复规则
                print("\n   🔍 检查排重效果:")
                
                # 检查版面链接规则排重
                page_rules = [rule['rule'] for rule in data.get('page_link_rules', [])]
                page_unique = len(set(page_rules))
                page_total = len(page_rules)
                print(f"   版面链接规则: {page_total} 个规则，{page_unique} 个唯一规则 {'✅' if page_total == page_unique else '❌'}")
                
                # 检查图片链接规则排重
                image_rules = [rule['rule'] for rule in data.get('image_link_rules', [])]
                image_unique = len(set(image_rules))
                image_total = len(image_rules)
                print(f"   图片链接规则: {image_total} 个规则，{image_unique} 个唯一规则 {'✅' if image_total == image_unique else '❌'}")
                
                # 检查PDF链接规则排重
                pdf_rules = [rule['rule'] for rule in data.get('pdf_link_rules', [])]
                pdf_unique = len(set(pdf_rules))
                pdf_total = len(pdf_rules)
                print(f"   PDF链接规则: {pdf_total} 个规则，{pdf_unique} 个唯一规则 {'✅' if pdf_total == pdf_unique else '❌'}")
                
                # 显示规则示例
                print("\n   📋 规则示例:")
                if data.get('page_link_rules'):
                    rule = data['page_link_rules'][0]
                    print(f"   版面规则: {rule['rule'][:80]}{'...' if len(rule['rule']) > 80 else ''}")
                    print(f"   方法: {rule['method']}, 来源: {rule['newspaper_name']}")
                
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"   ❌ API状态码错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ API请求失败: {e}")
        return False
    
    # 2. 测试页面访问
    print("\n2️⃣ 测试页面访问...")
    try:
        response = requests.get(f"{base_url}/add_newspaper", timeout=10)
        if response.status_code == 200:
            print("   ✅ 添加报纸页面可访问")
            
            # 检查修复后的JavaScript代码
            js_response = requests.get(f"{base_url}/static/js/add_newspaper.js", timeout=10)
            if js_response.status_code == 200:
                js_content = js_response.text
                
                # 检查关键修复点
                fixes = [
                    ('extractionRulesData', '全局规则数据变量'),
                    ('encodeURIComponent', 'URL编码处理'),
                    ('data-rule', '规则数据属性'),
                    ('window.extractionRulesData.page_link_rules[index]', '索引访问规则')
                ]
                
                print("   🔧 检查修复点:")
                for fix_code, description in fixes:
                    if fix_code in js_content:
                        print(f"   ✅ {description}")
                    else:
                        print(f"   ❌ {description} 缺失")
            else:
                print(f"   ❌ JavaScript文件访问失败: {js_response.status_code}")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 页面请求失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 修复测试完成！")
    
    print("\n📋 修复内容总结:")
    print("✅ 排重逻辑修复 - 只按规则内容排重，不考虑方法")
    print("✅ 规则截断修复 - 使用索引和全局变量避免HTML属性截断")
    print("✅ 数据传递优化 - 使用encodeURIComponent和data属性")
    print("✅ 选择函数重构 - 通过索引访问完整规则数据")
    
    print("\n🚀 使用说明:")
    print("1. 下拉框现在只显示唯一的提取规则")
    print("2. 选择规则后会完整填充到文本框中")
    print("3. 规则内容不会被截断或丢失")
    print("4. 支持复杂的XPath和正则表达式规则")
    
    return True

if __name__ == "__main__":
    test_fixed_functionality()
